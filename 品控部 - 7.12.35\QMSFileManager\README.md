# QMS文件管理器

## 📋 概述

QMS文件管理器是一个专为QMS质量管理系统设计的文件下载管理工具，由本地客户端应用程序和浏览器扩展组成，实现真正的指定路径下载和自动文件打开功能。

## 🎯 主要功能

### ✅ 核心特性
- **指定路径下载**: 文件直接下载到用户指定的本地文件夹
- **自动打开文件**: 下载完成后自动使用默认程序打开文件
- **智能文件处理**: PDF/图片预览，Office文档本地下载
- **下载进度显示**: 实时显示下载状态和进度
- **浏览器集成**: 无缝集成到QMS网页界面

### 🔧 技术特点
- **跨浏览器支持**: 主要支持Chrome，兼容Edge
- **本地应用**: Python桌面应用，轻量级高效
- **安全可靠**: 本地处理，不依赖第三方服务
- **易于部署**: 一键安装，简单配置

## 📦 系统组成

### 1. 本地客户端应用 (`app.py`)
- **GUI界面**: 基于tkinter的用户友好界面
- **HTTP服务器**: 监听端口8765，接收浏览器扩展请求
- **文件管理**: 处理文件下载、保存、打开
- **配置管理**: 用户设置的持久化存储

### 2. 浏览器扩展 (`browser_extension/`)
- **内容脚本**: 在QMS页面中运行，拦截下载请求
- **后台脚本**: 处理扩展逻辑和本地应用通信
- **弹出页面**: 扩展设置和状态管理界面
- **注入脚本**: 深度集成QMS页面功能

## 🚀 安装部署

### 系统要求
- **操作系统**: Windows 10/11 (主要支持)
- **Python**: 3.7或更高版本
- **浏览器**: Chrome 88+ 或 Edge 88+
- **网络**: 能够访问QMS服务器 (**************:5000)

### 安装步骤

#### 1. 安装本地应用
```bash
# 1. 解压QMSFileManager文件夹到本地
# 2. 双击运行 install.bat
# 3. 按照提示完成安装
```

#### 2. 安装浏览器扩展
```bash
# 1. 打开Chrome浏览器
# 2. 访问 chrome://extensions/
# 3. 开启"开发者模式"
# 4. 点击"加载已解压的扩展程序"
# 5. 选择 QMSFileManager/browser_extension 文件夹
```

#### 3. 配置设置
```bash
# 1. 启动QMS文件管理器应用
# 2. 设置下载路径 (默认: C:\QMS1\)
# 3. 确认服务器地址正确
# 4. 保存设置
```

## 🎮 使用指南

### 基本使用流程

1. **启动应用**
   - 双击桌面快捷方式启动QMS文件管理器
   - 确认状态显示为"就绪 - 等待下载请求"

2. **访问QMS系统**
   - 打开Chrome浏览器
   - 访问QMS系统 (http://**************:5000)
   - 确认右上角显示"QMS文件管理器 (已连接)"

3. **下载文件**
   - 在QMS系统中点击任意附件的"打开"按钮
   - 系统自动判断文件类型：
     - **PDF/图片**: 浏览器中预览
     - **Office/CAD文档**: 下载到本地并自动打开

4. **查看结果**
   - 文件下载到指定路径 (默认: C:\QMS1\)
   - 下载完成后自动打开文件
   - 在应用程序中查看下载历史

### 高级功能

#### 自定义下载路径
```bash
# 在QMS文件管理器中:
# 1. 修改"下载路径"设置
# 2. 点击"浏览"选择文件夹
# 3. 点击"保存设置"
```

#### 扩展设置
```bash
# 点击浏览器扩展图标:
# 1. 查看连接状态
# 2. 修改本地应用地址
# 3. 开启/关闭自动下载
# 4. 测试连接状态
```

## 🔧 配置说明

### 本地应用配置
```json
{
  "download_path": "C:\\QMS1\\",
  "auto_open": true,
  "server_port": 8765,
  "server_host": "**************:5000"
}
```

### 扩展配置
- **本地服务器地址**: http://localhost:8765
- **自动下载**: 启用
- **QMS服务器**: **************:5000

## 🐛 故障排除

### 常见问题

#### 1. 扩展显示"未连接"
**原因**: 本地应用未启动或端口被占用
**解决**: 
- 确认QMS文件管理器应用正在运行
- 检查端口8765是否被其他程序占用
- 重启本地应用

#### 2. 下载失败
**原因**: 网络连接问题或文件权限问题
**解决**:
- 检查网络连接到QMS服务器
- 确认下载路径有写入权限
- 查看应用程序错误日志

#### 3. 文件无法自动打开
**原因**: 文件关联程序未安装或路径错误
**解决**:
- 安装对应的文件查看程序 (Office、CAD软件等)
- 检查文件是否正确下载到指定路径
- 手动打开文件测试

#### 4. 扩展无法加载
**原因**: 浏览器版本不兼容或扩展文件损坏
**解决**:
- 更新Chrome浏览器到最新版本
- 重新下载扩展文件
- 检查扩展权限设置

### 日志查看
- **本地应用日志**: 在应用程序界面查看状态信息
- **浏览器日志**: 按F12打开开发者工具，查看Console
- **扩展日志**: 在chrome://extensions/中查看扩展详情

## 📞 技术支持

### 联系方式
- **技术支持**: 品质中心IT部门
- **问题反馈**: 通过QMS系统提交工单
- **紧急联系**: 内部技术支持热线

### 更新维护
- **版本检查**: 定期检查是否有新版本
- **自动更新**: 扩展支持自动更新
- **手动更新**: 重新下载安装包

## 📄 版本信息

- **当前版本**: v1.0.0
- **发布日期**: 2025-01-27
- **兼容性**: QMS系统 v2.0+
- **支持浏览器**: Chrome 88+, Edge 88+

## 🔒 安全说明

- **本地处理**: 所有文件处理在本地进行，不上传到第三方
- **权限最小化**: 扩展仅请求必要的浏览器权限
- **数据保护**: 用户设置和下载历史仅存储在本地
- **网络安全**: 仅与指定的QMS服务器通信

---

**QMS文件管理器** - 让文件下载更智能、更便捷！
