// QMS文件管理器 - 注入脚本
// 在页面上下文中运行，可以访问页面的JavaScript函数

(function() {
    'use strict';
    
    console.log('QMS文件管理器注入脚本已加载');
    
    // 保存原始的openAttachment函数
    let originalOpenAttachment = null;
    
    // 等待页面加载完成
    function waitForPageReady() {
        if (typeof window.openAttachment === 'function') {
            interceptOpenAttachment();
        } else {
            // 如果函数还没有定义，等待一段时间后重试
            setTimeout(waitForPageReady, 1000);
        }
    }
    
    // 拦截openAttachment函数
    function interceptOpenAttachment() {
        if (window.openAttachment && !originalOpenAttachment) {
            originalOpenAttachment = window.openAttachment;
            
            window.openAttachment = function(fileName, url, fileExtension) {
                console.log('拦截到openAttachment调用:', { fileName, url, fileExtension });
                
                // 检查是否需要下载（非预览文件）
                const extension = fileExtension ? fileExtension.toLowerCase() : '';
                const inlineTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'txt', 'html', 'htm', 'xml', 'json'];
                
                if (inlineTypes.includes(extension)) {
                    // 预览文件，使用原始函数
                    console.log('预览文件，使用原始函数');
                    return originalOpenAttachment.call(this, fileName, url, fileExtension);
                } else {
                    // 需要下载的文件，发送到扩展
                    console.log('下载文件，发送到扩展');
                    
                    // 构建完整URL
                    let fullUrl = url;
                    if (!fullUrl.startsWith('http')) {
                        fullUrl = window.location.origin + (url.startsWith('/') ? url : '/' + url);
                    }
                    
                    // 发送下载请求到content script
                    window.postMessage({
                        type: 'QMS_DOWNLOAD_REQUEST',
                        payload: {
                            url: fullUrl,
                            filename: fileName,
                            extension: fileExtension,
                            source: 'openAttachment'
                        }
                    }, '*');
                    
                    return false; // 阻止原始函数执行
                }
            };
            
            console.log('openAttachment函数已被拦截');
        }
    }
    
    // 拦截其他可能的下载函数
    function interceptOtherDownloadFunctions() {
        // 拦截downloadAndOpenFile函数（如果存在）
        if (window.downloadAndOpenFile && typeof window.downloadAndOpenFile === 'function') {
            const originalDownloadAndOpenFile = window.downloadAndOpenFile;
            
            window.downloadAndOpenFile = function(url, fileName, fileExtension) {
                console.log('拦截到downloadAndOpenFile调用:', { url, fileName, fileExtension });
                
                // 构建完整URL
                let fullUrl = url;
                if (!fullUrl.startsWith('http')) {
                    fullUrl = window.location.origin + (url.startsWith('/') ? url : '/' + url);
                }
                
                // 发送下载请求到content script
                window.postMessage({
                    type: 'QMS_DOWNLOAD_REQUEST',
                    payload: {
                        url: fullUrl,
                        filename: fileName,
                        extension: fileExtension,
                        source: 'downloadAndOpenFile'
                    }
                }, '*');
                
                return false; // 阻止原始函数执行
            };
            
            console.log('downloadAndOpenFile函数已被拦截');
        }
        
        // 拦截downloadToClientDevice函数（如果存在）
        if (window.downloadToClientDevice && typeof window.downloadToClientDevice === 'function') {
            const originalDownloadToClientDevice = window.downloadToClientDevice;
            
            window.downloadToClientDevice = function(url, fileName, fileExtension) {
                console.log('拦截到downloadToClientDevice调用:', { url, fileName, fileExtension });
                
                // 构建完整URL
                let fullUrl = url;
                if (!fullUrl.startsWith('http')) {
                    fullUrl = window.location.origin + (url.startsWith('/') ? url : '/' + url);
                }
                
                // 发送下载请求到content script
                window.postMessage({
                    type: 'QMS_DOWNLOAD_REQUEST',
                    payload: {
                        url: fullUrl,
                        filename: fileName,
                        extension: fileExtension,
                        source: 'downloadToClientDevice'
                    }
                }, '*');
                
                return false; // 阻止原始函数执行
            };
            
            console.log('downloadToClientDevice函数已被拦截');
        }
    }
    
    // 监听页面上的点击事件，拦截下载链接
    function interceptDownloadLinks() {
        document.addEventListener('click', function(event) {
            const target = event.target;
            const link = target.closest('a[href*="/download"]');
            
            if (link) {
                const href = link.getAttribute('href');
                if (href && href.includes('/download')) {
                    console.log('拦截到下载链接点击:', href);
                    
                    // 阻止默认行为
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // 尝试从链接或周围元素获取文件名
                    let fileName = link.getAttribute('data-filename') || 
                                  link.getAttribute('title') ||
                                  link.textContent.trim();
                    
                    if (!fileName) {
                        // 从URL中提取文件名
                        const urlParts = href.split('/');
                        fileName = urlParts[urlParts.length - 1] || 'download';
                    }
                    
                    // 构建完整URL
                    let fullUrl = href;
                    if (!fullUrl.startsWith('http')) {
                        fullUrl = window.location.origin + (href.startsWith('/') ? href : '/' + href);
                    }
                    
                    // 发送下载请求
                    window.postMessage({
                        type: 'QMS_DOWNLOAD_REQUEST',
                        payload: {
                            url: fullUrl,
                            filename: fileName,
                            source: 'link_click'
                        }
                    }, '*');
                    
                    return false;
                }
            }
        }, true);
    }
    
    // 添加扩展状态显示
    function addExtensionIndicator() {
        // 检查是否已经添加了指示器
        if (document.getElementById('qms-extension-injected-indicator')) {
            return;
        }
        
        const indicator = document.createElement('div');
        indicator.id = 'qms-extension-injected-indicator';
        indicator.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            z-index: 9999;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-family: Arial, sans-serif;
            pointer-events: none;
            opacity: 0.7;
        `;
        indicator.textContent = 'QMS扩展已激活';
        
        document.body.appendChild(indicator);
        
        // 3秒后淡出
        setTimeout(() => {
            indicator.style.transition = 'opacity 1s ease-out';
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 1000);
        }, 3000);
    }
    
    // 重写fetch函数以拦截API请求
    function interceptFetchRequests() {
        const originalFetch = window.fetch;
        
        window.fetch = function(...args) {
            const url = args[0];
            
            // 检查是否是下载相关的API请求
            if (typeof url === 'string' && url.includes('/download')) {
                console.log('拦截到fetch下载请求:', url);
                
                // 可以在这里添加额外的处理逻辑
                // 但通常我们让正常的fetch继续执行
            }
            
            return originalFetch.apply(this, args);
        };
    }
    
    // 初始化所有拦截功能
    function initialize() {
        console.log('初始化QMS文件管理器注入脚本');
        
        // 等待页面函数加载
        waitForPageReady();
        
        // 拦截其他下载函数
        setTimeout(interceptOtherDownloadFunctions, 2000);
        
        // 拦截下载链接
        interceptDownloadLinks();
        
        // 拦截fetch请求
        interceptFetchRequests();
        
        // 添加扩展指示器
        addExtensionIndicator();
        
        console.log('QMS文件管理器注入脚本初始化完成');
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // 也可以在window.onload时再次尝试
    window.addEventListener('load', function() {
        setTimeout(() => {
            interceptOtherDownloadFunctions();
        }, 1000);
    });
    
})();
