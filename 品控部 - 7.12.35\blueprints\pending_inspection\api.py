# -*- coding: utf-8 -*-
"""
待检物料管理API
"""

from flask import Blueprint, request, jsonify, session
from db_config import get_db_connection
from datetime import datetime, date
import json

pending_inspection_bp = Blueprint('pending_inspection', __name__)

@pending_inspection_bp.route('/api/pending_inspections/smart_batch_import', methods=['POST'])
def smart_batch_import_pending_inspections():
    """智能批量导入待检物料 - 根据物料预设检验类型自动分流"""
    try:
        data = request.get_json()
        materials = data.get('materials', [])
        batch_name = data.get('batch_name', f"智能导入_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

        if not materials:
            return jsonify({"success": False, "error": "没有提供物料数据"}), 400

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 统计结果
        sampling_count = 0
        full_count = 0
        exempt_count = 0
        error_items = []
        exempt_records = []  # 免检物料直接录入检验记录

        # 为每种检验类型创建批次
        sampling_batch_id = None
        full_batch_id = None

        for material in materials:
            try:
                material_code = material.get('material_code', '').strip()
                if not material_code:
                    error_items.append({"material_code": material_code, "error": "物料料号不能为空"})
                    continue

                # 获取物料基础信息和预设检验类型
                material_info = get_material_info_with_inspection_type(cursor, material_code)

                # 确定检验类型 - 优先使用用户选择的类型，否则使用物料预设
                user_selected_type = material.get('inspection_type', '').strip()
                if user_selected_type:
                    # 转换用户选择的类型
                    type_mapping = {
                        '抽样': 'sampling',
                        '全部': 'full',
                        '免检': 'exempt'
                    }
                    inspection_type = type_mapping.get(user_selected_type, 'sampling')
                else:
                    # 使用物料预设检验类型
                    inspection_type = determine_inspection_type(material_info)

                # 获取最近一次检验的供应商
                latest_supplier = get_latest_supplier(cursor, material_code)

                # 准备插入数据
                insert_data = {
                    'material_code': material_code,
                    'material_name': material_info.get('material_name') or material.get('material_name', ''),
                    'specification': material_info.get('specification') or material.get('specification', ''),
                    'supplier_name': material.get('supplier_name') or latest_supplier or '',
                    'purchase_order': material.get('purchase_order', ''),
                    'incoming_quantity': material.get('incoming_quantity', 0),
                    'unit': material_info.get('unit') or material.get('unit', 'PCS'),
                    'batch_number': material.get('batch_number', ''),
                    'arrival_date': material.get('arrival_date') or date.today(),
                    'planned_inspection_date': material.get('planned_inspection_date'),
                    'inspector': material.get('inspector', ''),
                    'remarks': material.get('remarks', ''),
                    'created_by': session.get('username', 'system')
                }

                if inspection_type == 'exempt':
                    # 免检物料直接录入检验记录
                    exempt_record = create_exempt_inspection_record(cursor, insert_data)
                    exempt_records.append(exempt_record)
                    exempt_count += 1
                else:
                    # 创建对应的批次（如果还没创建）
                    if inspection_type == 'sampling' and sampling_batch_id is None:
                        sampling_batch_id = create_inspection_batch(cursor, f"{batch_name}_抽样检验", 'sampling')
                    elif inspection_type == 'full' and full_batch_id is None:
                        full_batch_id = create_inspection_batch(cursor, f"{batch_name}_全部检验", 'full')

                    # 设置批次ID
                    batch_id = sampling_batch_id if inspection_type == 'sampling' else full_batch_id
                    insert_data['batch_id'] = batch_id
                    insert_data['inspection_type'] = inspection_type

                    # 插入待检物料记录
                    cursor.execute("""
                        INSERT INTO pending_inspections (
                            material_code, material_name, specification, supplier_name, purchase_order,
                            incoming_quantity, unit, inspection_type, batch_number,
                            arrival_date, planned_inspection_date, inspector, remarks,
                            batch_id, created_by
                        ) VALUES (
                            %(material_code)s, %(material_name)s, %(specification)s, %(supplier_name)s, %(purchase_order)s,
                            %(incoming_quantity)s, %(unit)s, %(inspection_type)s, %(batch_number)s,
                            %(arrival_date)s, %(planned_inspection_date)s, %(inspector)s, %(remarks)s,
                            %(batch_id)s, %(created_by)s
                        )
                    """, insert_data)

                    if inspection_type == 'sampling':
                        sampling_count += 1
                    else:
                        full_count += 1

            except Exception as e:
                error_items.append({"material_code": material_code, "error": str(e)})

        conn.commit()

        total_count = sampling_count + full_count + exempt_count

        return jsonify({
            "success": True,
            "message": f"智能导入完成！抽样检验: {sampling_count}个，全部检验: {full_count}个，免检: {exempt_count}个",
            "data": {
                "total_count": total_count,
                "sampling_batch_id": sampling_batch_id,
                "full_batch_id": full_batch_id,
                "sampling_count": sampling_count,
                "full_count": full_count,
                "exempt_count": exempt_count,
                "exempt_records": exempt_records,
                "error_count": len(error_items),
                "error_items": error_items
            }
        })

    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@pending_inspection_bp.route('/api/pending_inspections', methods=['GET'])
def get_pending_inspections():
    """获取待检物料列表"""
    try:
        inspection_type = request.args.get('type', 'sampling')  # sampling 或 full
        status = request.args.get('status', 'pending')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 构建查询条件
        where_conditions = ["inspection_type = %s"]
        params = [inspection_type]
        
        if status and status != 'all':
            where_conditions.append("status = %s")
            params.append(status)
        
        # 计算总数
        count_sql = f"""
            SELECT COUNT(*) as total
            FROM pending_inspections
            WHERE {' AND '.join(where_conditions)}
        """
        cursor.execute(count_sql, params)
        total = cursor.fetchone()['total']
        
        # 获取分页数据
        offset = (page - 1) * per_page
        data_sql = f"""
            SELECT
                id,
                material_code,
                material_name,
                specification,
                supplier_name,
                purchase_order,
                incoming_quantity,
                unit,
                status,
                batch_number,
                arrival_date,
                planned_inspection_date,
                inspector,
                remarks,
                created_by,
                created_at,
                inspection_record_id
            FROM pending_inspections
            WHERE {' AND '.join(where_conditions)}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([per_page, offset])
        cursor.execute(data_sql, params)
        items = cursor.fetchall()
        
        # 格式化日期
        for item in items:
            if item['arrival_date']:
                item['arrival_date'] = item['arrival_date'].strftime('%Y-%m-%d')
            if item['planned_inspection_date']:
                item['planned_inspection_date'] = item['planned_inspection_date'].strftime('%Y-%m-%d')
            if item['created_at']:
                item['created_at'] = item['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify({
            "success": True,
            "data": {
                "items": items,
                "total": total,
                "page": page,
                "per_page": per_page,
                "pages": (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@pending_inspection_bp.route('/api/pending_inspections/batch_import', methods=['POST'])
def batch_import_pending_inspections():
    """批量导入待检物料"""
    try:
        data = request.get_json()
        inspection_type = data.get('inspection_type', 'sampling')
        materials = data.get('materials', [])
        batch_name = data.get('batch_name', f"批次_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

        if not materials:
            return jsonify({"success": False, "error": "没有提供物料数据"}), 400

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 创建批次记录
        cursor.execute("""
            INSERT INTO pending_inspection_batches (batch_name, inspection_type, total_items, pending_items, created_by)
            VALUES (%s, %s, %s, %s, %s)
        """, (batch_name, inspection_type, len(materials), len(materials), session.get('username', 'system')))

        batch_id = cursor.lastrowid

        success_count = 0
        error_items = []

        for material in materials:
            try:
                material_code = material.get('material_code', '').strip()
                if not material_code:
                    error_items.append({"material_code": material_code, "error": "物料料号不能为空"})
                    continue

                # 获取物料基础信息
                material_info = get_material_info(cursor, material_code)

                # 获取最近一次检验的供应商
                latest_supplier = get_latest_supplier(cursor, material_code)
                
                # 准备插入数据
                insert_data = {
                    'material_code': material_code,
                    'material_name': material_info.get('name', material.get('material_name', '')),
                    'specification': material_info.get('specification', material.get('specification', '')),
                    'supplier_name': latest_supplier or material.get('supplier_name', ''),
                    'purchase_order': material.get('purchase_order', ''),
                    'incoming_quantity': material.get('incoming_quantity'),
                    'unit': material_info.get('unit', material.get('unit', '')),
                    'inspection_type': inspection_type,
                    'batch_number': material.get('batch_number', ''),
                    'arrival_date': material.get('arrival_date'),
                    'planned_inspection_date': material.get('planned_inspection_date'),
                    'inspector': material.get('inspector', ''),
                    'remarks': material.get('remarks', ''),
                    'batch_id': batch_id,
                    'created_by': session.get('username', 'system')
                }
                
                # 插入待检物料记录
                cursor.execute("""
                    INSERT INTO pending_inspections (
                        material_code, material_name, specification, supplier_name, purchase_order,
                        incoming_quantity, unit, inspection_type, batch_number,
                        arrival_date, planned_inspection_date, inspector, remarks,
                        batch_id, created_by
                    ) VALUES (
                        %(material_code)s, %(material_name)s, %(specification)s, %(supplier_name)s, %(purchase_order)s,
                        %(incoming_quantity)s, %(unit)s, %(inspection_type)s, %(batch_number)s,
                        %(arrival_date)s, %(planned_inspection_date)s, %(inspector)s, %(remarks)s,
                        %(batch_id)s, %(created_by)s
                    )
                """, insert_data)
                
                success_count += 1
                
            except Exception as e:
                error_items.append({"material_code": material_code, "error": str(e)})
        
        conn.commit()
        
        return jsonify({
            "success": True,
            "message": f"批量导入完成，成功导入 {success_count} 个物料",
            "data": {
                "batch_id": batch_id,
                "success_count": success_count,
                "error_count": len(error_items),
                "error_items": error_items
            }
        })
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@pending_inspection_bp.route('/api/pending_inspections/<int:item_id>', methods=['GET'])
def get_pending_inspection(item_id):
    """获取单个待检物料信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute("""
            SELECT * FROM pending_inspections
            WHERE id = %s
        """, (item_id,))

        item = cursor.fetchone()
        if not item:
            return jsonify({"success": False, "error": "待检物料不存在"}), 404

        # 转换日期格式
        if item.get('arrival_date'):
            item['arrival_date'] = item['arrival_date'].strftime('%Y-%m-%d')
        if item.get('planned_inspection_date'):
            item['planned_inspection_date'] = item['planned_inspection_date'].strftime('%Y-%m-%d')
        if item.get('created_at'):
            item['created_at'] = item['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if item.get('updated_at'):
            item['updated_at'] = item['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({"success": True, "data": item})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@pending_inspection_bp.route('/api/pending_inspections/<int:item_id>', methods=['PUT'])
def update_pending_inspection(item_id):
    """更新待检物料信息"""
    try:
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建更新字段
        update_fields = []
        params = []
        
        allowed_fields = [
            'material_name', 'specification', 'supplier_name', 'purchase_order', 'incoming_quantity',
            'unit', 'batch_number', 'arrival_date', 'planned_inspection_date',
            'inspector', 'remarks'
        ]
        
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = %s")
                params.append(data[field])
        
        if not update_fields:
            return jsonify({"success": False, "error": "没有提供更新数据"}), 400
        
        update_fields.append("updated_by = %s")
        update_fields.append("updated_at = %s")
        params.extend([session.get('username', 'system'), datetime.now()])
        params.append(item_id)
        
        cursor.execute(f"""
            UPDATE pending_inspections 
            SET {', '.join(update_fields)}
            WHERE id = %s
        """, params)
        
        if cursor.rowcount == 0:
            return jsonify({"success": False, "error": "待检物料不存在"}), 404
        
        conn.commit()
        
        return jsonify({"success": True, "message": "更新成功"})
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@pending_inspection_bp.route('/api/pending_inspections/<int:item_id>', methods=['DELETE'])
def delete_pending_inspection(item_id):
    """删除待检物料"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM pending_inspections WHERE id = %s", (item_id,))
        
        if cursor.rowcount == 0:
            return jsonify({"success": False, "error": "待检物料不存在"}), 404
        
        conn.commit()
        
        return jsonify({"success": True, "message": "删除成功"})
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@pending_inspection_bp.route('/api/pending_inspections/<int:item_id>/start_inspection', methods=['POST'])
def start_inspection(item_id):
    """开始检验（创建检验记录）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取待检物料信息
        cursor.execute("""
            SELECT * FROM pending_inspections 
            WHERE id = %s AND status = 'pending'
        """, (item_id,))
        
        pending_item = cursor.fetchone()
        if not pending_item:
            return jsonify({"success": False, "error": "待检物料不存在或状态不正确"}), 404
        
        # 创建检验记录
        inspection_data = {
            'material_code': pending_item['material_code'],
            'material_name': pending_item['material_name'],
            'specification': pending_item['specification'],
            'supplier_name': pending_item['supplier_name'],
            'purchase_order': pending_item.get('purchase_order', ''),
            'incoming_quantity': pending_item['incoming_quantity'],
            'unit': pending_item['unit'],
            'batch_number': pending_item['batch_number'],
            'arrival_date': pending_item['arrival_date'],
            'inspector': pending_item['inspector'] or session.get('username', ''),
            'remarks': pending_item['remarks'],
            'created_by': session.get('username', 'system')
        }
        
        # 只支持抽样检验
        if pending_item['inspection_type'] != 'sampling':
            return jsonify({"success": False, "error": "不支持的检验类型，只支持抽样检验"}), 400

        cursor.execute("""
            INSERT INTO sampling_inspection_records (
                material_code, material_name, specification, supplier_name, purchase_order,
                incoming_quantity, unit, batch_number, arrival_date,
                inspector, remarks, created_by
            ) VALUES (
                %(material_code)s, %(material_name)s, %(specification)s, %(supplier_name)s, %(purchase_order)s,
                %(incoming_quantity)s, %(unit)s, %(batch_number)s, %(arrival_date)s,
                %(inspector)s, %(remarks)s, %(created_by)s
            )
        """, inspection_data)
        
        inspection_record_id = cursor.lastrowid
        
        # 更新待检物料状态
        cursor.execute("""
            UPDATE pending_inspections 
            SET status = 'in_progress', inspection_record_id = %s, updated_by = %s, updated_at = %s
            WHERE id = %s
        """, (inspection_record_id, session.get('username', 'system'), datetime.now(), item_id))
        
        conn.commit()
        
        return jsonify({
            "success": True,
            "message": "检验记录创建成功",
            "data": {
                "inspection_record_id": inspection_record_id,
                "inspection_type": pending_item['inspection_type']
            }
        })
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def get_material_info_with_inspection_type(cursor, material_code):
    """获取物料信息包括检验类型"""
    cursor.execute("""
        SELECT material_number, material_name, specification, material_type, color,
               material_category, inspection_type, unit
        FROM materials
        WHERE material_number = %s
    """, (material_code,))

    result = cursor.fetchone()
    if result:
        return result
    else:
        return {
            'material_number': material_code,
            'material_name': '',
            'specification': '',
            'material_type': '',
            'color': '',
            'material_category': '',
            'inspection_type': '',
            'unit': 'PCS'
        }

def determine_inspection_type(material_info):
    """根据物料信息确定检验类型"""
    inspection_type = material_info.get('inspection_type', '').strip()

    # 标准化检验类型
    if inspection_type in ['抽样', '抽样检验']:
        return 'sampling'
    elif inspection_type in ['全检', '全部', '全部检验']:
        return 'full'
    elif inspection_type in ['免检']:
        return 'exempt'
    else:
        # 默认为抽样检验
        return 'sampling'

def create_inspection_batch(cursor, batch_name, inspection_type):
    """创建检验批次"""
    cursor.execute("""
        INSERT INTO pending_inspection_batches (batch_name, inspection_type, total_items, pending_items, created_by)
        VALUES (%s, %s, %s, %s, %s)
    """, (batch_name, inspection_type, 0, 0, session.get('username', 'system')))

    return cursor.lastrowid

def create_exempt_inspection_record(cursor, material_data):
    """为免检物料创建检验记录"""
    # 生成检验报告编号
    report_code = generate_report_code('EXEMPT')

    # 插入到对应的检验记录表（这里假设免检记录也放在抽样检验表中，但标记为免检）
    cursor.execute("""
        INSERT INTO sampling_inspection (
            report_code, material_number, material_name, specification,
            supplier, purchase_order, receipt_date, inspection_date,
            total_quantity, sample_quantity, qualified_quantity, defect_quantity,
            defect_issues, inspector, created_at
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
    """, (
        report_code,
        material_data['material_code'],
        material_data['material_name'],
        material_data['specification'],
        material_data['supplier_name'],
        material_data['purchase_order'],
        material_data['arrival_date'],
        date.today(),  # 检验日期为今天
        material_data['incoming_quantity'],
        0,  # 免检无需抽样
        material_data['incoming_quantity'],  # 免检默认全部合格
        0,  # 免检无缺陷
        '免检物料，无需检验',
        material_data['inspector'] or '系统',
        datetime.now()
    ))

    return {
        'report_code': report_code,
        'material_code': material_data['material_code'],
        'material_name': material_data['material_name'],
        'status': '免检完成'
    }

def generate_report_code(prefix='EXEMPT'):
    """生成检验报告编号"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"{prefix}{timestamp}"

def get_material_info(cursor, material_code):
    """获取物料基础信息"""
    try:
        cursor.execute("""
            SELECT name, specification, unit
            FROM materials
            WHERE code = %s
        """, (material_code,))
        
        result = cursor.fetchone()
        if result:
            return {
                'name': result['name'],
                'specification': result['specification'],
                'unit': result['unit']
            }
        return {}
    except:
        return {}

def get_latest_supplier(cursor, material_code):
    """获取最近一次检验的供应商"""
    try:
        # 先查抽样检验记录
        cursor.execute("""
            SELECT supplier_name
            FROM sampling_inspection_records
            WHERE material_code = %s AND supplier_name IS NOT NULL AND supplier_name != ''
            ORDER BY created_at DESC
            LIMIT 1
        """, (material_code,))
        
        result = cursor.fetchone()
        if result:
            return result['supplier_name']
        
        # 再查全部检验记录
        cursor.execute("""
            SELECT supplier_name
            FROM full_inspection_records
            WHERE material_code = %s AND supplier_name IS NOT NULL AND supplier_name != ''
            ORDER BY created_at DESC
            LIMIT 1
        """, (material_code,))
        
        result = cursor.fetchone()
        if result:
            return result['supplier_name']
        
        # 最后查物料信息中的供应商
        cursor.execute("""
            SELECT supplier_name
            FROM materials
            WHERE code = %s AND supplier_name IS NOT NULL AND supplier_name != ''
        """, (material_code,))
        
        result = cursor.fetchone()
        if result:
            return result['supplier_name']
        
        return None
    except:
        return None


@pending_inspection_bp.route('/api/pending-inspection/batch-update', methods=['POST'])
def batch_update_pending_inspections():
    """批量更新待检物料"""
    try:
        data = request.get_json()
        items = data.get('items', [])

        if not items:
            return jsonify({'success': False, 'message': '没有数据需要更新'})

        conn = get_db_connection()
        cursor = conn.cursor()

        updated_count = 0

        for item in items:
            item_id = item.get('id')
            if not item_id:
                continue

            # 构建更新字段
            update_fields = []
            update_values = []

            # 允许更新的字段
            allowed_fields = [
                'material_name', 'specification', 'supplier_name', 'purchase_order',
                'incoming_quantity', 'unit', 'batch_number', 'arrival_date',
                'planned_inspection_date', 'inspector', 'remarks'
            ]

            for field in allowed_fields:
                if field in item:
                    update_fields.append(f"{field} = %s")
                    update_values.append(item[field])

            if update_fields:
                update_values.append(item_id)

                update_sql = f"""
                    UPDATE pending_inspections
                    SET {', '.join(update_fields)}
                    WHERE id = %s
                """

                cursor.execute(update_sql, update_values)
                updated_count += 1

        conn.commit()

        return jsonify({
            'success': True,
            'message': f'成功更新 {updated_count} 条记录',
            'updated_count': updated_count
        })

    except Exception as e:
        print(f"批量更新失败: {e}")
        return jsonify({'success': False, 'message': f'批量更新失败: {str(e)}'})
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
