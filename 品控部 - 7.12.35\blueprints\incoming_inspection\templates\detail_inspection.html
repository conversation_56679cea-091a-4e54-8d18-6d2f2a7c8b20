{% extends "base.html" %}

{% block title %}检验记录详情 - 品质中心管理系统{% endblock %}

{% block content %}
<div class="page-header">
    <h1>检验记录详情</h1>
    <div class="btn-group">
        <button class="btn btn-secondary" onclick="history.back()">返回</button>
        <button class="btn btn-primary" id="print-btn">打印报告</button>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h4>检验信息</h4>
    </div>
    <div class="card-body">
        <div class="two-column-layout">
            <!-- 左侧列 - 基本信息 -->
            <div class="column left-column">
                <div class="info-section">
                    <h5 class="section-title">基本信息</h5>
                    <div class="info-row">
                        <div class="info-label">物料料号:</div>
                        <div class="info-value" id="material-number"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">物料名称:</div>
                        <div class="info-value" id="material-name"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">规格:</div>
                        <div class="info-value" id="specification"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">材质:</div>
                        <div class="info-value" id="material-type"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">颜色:</div>
                        <div class="info-value" id="color"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">供应商:</div>
                        <div class="info-value" id="supplier"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">采购单号:</div>
                        <div class="info-value" id="purchase-order"></div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧列 - 检验信息 -->
            <div class="column right-column">
                <div class="info-section">
                    <h5 class="section-title">检验结果</h5>
                    <div class="info-row">
                        <div class="info-label">来料日期:</div>
                        <div class="info-value" id="receipt-date"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">检验日期:</div>
                        <div class="info-value" id="inspection-date"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">来料数量:</div>
                        <div class="info-value" id="total-quantity"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">抽样数量:</div>
                        <div class="info-value" id="sample-quantity"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">不良数量:</div>
                        <div class="info-value" id="defect-quantity"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">合格率:</div>
                        <div class="info-value" id="qualified-rate"></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">检验员:</div>
                        <div class="info-value" id="inspector"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h4>不良问题详情</h4>
    </div>
    <div class="card-body">
        <div id="issues-container">
            <!-- 这里将动态插入不良问题记录 -->
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 从URL获取记录ID
    const urlParams = new URLSearchParams(window.location.search);
    const recordId = urlParams.get('id');
    
    if (!recordId) {
        alert('缺少记录ID');
        history.back();
        return;
    }
    
    // 获取检验记录详情
    fetch(`/sampling_inspection/api/inspection_details/${recordId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const record = data.record;
                
                // 填充基本信息
                document.getElementById('material-number').textContent = record.material_number;
                document.getElementById('material-name').textContent = record.material_name;
                document.getElementById('specification').textContent = record.specification || '-';
                document.getElementById('material-type').textContent = record.material_type || '-';
                document.getElementById('color').textContent = record.color || '-';
                document.getElementById('supplier').textContent = record.supplier;
                document.getElementById('purchase-order').textContent = record.purchase_order;
                document.getElementById('receipt-date').textContent = new Date(record.receipt_date).toLocaleDateString();
                document.getElementById('inspection-date').textContent = new Date(record.inspection_date).toLocaleDateString();
                document.getElementById('total-quantity').textContent = record.total_quantity;
                document.getElementById('sample-quantity').textContent = record.sample_quantity;
                document.getElementById('defect-quantity').textContent = record.defect_quantity;
                document.getElementById('inspector').textContent = record.inspector || '-';
                
                // 计算合格率
                const qualifiedRate = (record.sample_quantity / record.total_quantity * 100).toFixed(2);
                document.getElementById('qualified-rate').textContent = `${qualifiedRate}%`;
                
                // 处理不良问题
                const issuesContainer = document.getElementById('issues-container');
                let issues;
                
                try {
                    issues = JSON.parse(record.defect_issues || '[]');
                } catch (e) {
                    // 如果是旧格式的纯文本不良问题
                    if (record.defect_issues && record.defect_issues.trim()) {
                        issues = [{
                            type: '不良问题',
                            description: record.defect_issues,
                            images: []
                        }];
                    } else {
                        issues = [];
                    }
                }
                
                if (issues.length === 0) {
                    issuesContainer.innerHTML = '<div class="alert alert-info">无不良问题记录</div>';
                    return;
                }
                
                // 为每个问题点创建卡片
                issues.forEach((issue, index) => {
                    const issueCard = document.createElement('div');
                    issueCard.className = 'card mb-3';
                    
                    const issueHeader = document.createElement('div');
                    issueHeader.className = 'card-header bg-light';
                    issueHeader.innerHTML = `<h5 class="mb-0">问题点 ${index + 1}: ${issue.type || '未分类'}</h5>`;
                    
                    const issueBody = document.createElement('div');
                    issueBody.className = 'card-body';
                    
                    // 问题描述
                    const descDiv = document.createElement('div');
                    descDiv.className = 'mb-3';
                    descDiv.innerHTML = `<p><strong>问题描述:</strong> ${issue.description}</p>`;
                    issueBody.appendChild(descDiv);
                    
                    // 处理图片
                    if (issue.images && issue.images.length > 0) {
                        const imagesDiv = document.createElement('div');
                        imagesDiv.className = 'mt-2';
                        imagesDiv.innerHTML = '<h6>问题图片:</h6>';
                        
                        const imageGallery = document.createElement('div');
                        imageGallery.className = 'd-flex flex-wrap';
                        
                        issue.images.forEach(imagePath => {
                            const imageWrapper = document.createElement('div');
                            imageWrapper.className = 'm-2 border image-thumbnail';
                            imageWrapper.style.width = '150px';
                            imageWrapper.style.height = '150px';
                            imageWrapper.style.overflow = 'hidden';
                            imageWrapper.style.cursor = 'pointer';
                            
                            const image = document.createElement('img');
                            image.src = `/static/uploads/${imagePath}`;
                            image.alt = '问题图片';
                            image.className = 'img-fluid';
                            image.style.width = '100%';
                            image.style.height = '100%';
                            image.style.objectFit = 'cover';
                            
                            // 点击图片放大
                            imageWrapper.addEventListener('click', function() {
                                const modal = document.createElement('div');
                                modal.className = 'modal';
                                modal.style.display = 'block';
                                modal.style.position = 'fixed';
                                modal.style.zIndex = '1000';
                                modal.style.left = '0';
                                modal.style.top = '0';
                                modal.style.width = '100%';
                                modal.style.height = '100%';
                                modal.style.overflow = 'auto';
                                modal.style.backgroundColor = 'rgba(0,0,0,0.9)';
                                
                                const modalContent = document.createElement('div');
                                modalContent.style.margin = '5% auto';
                                modalContent.style.padding = '20px';
                                modalContent.style.maxWidth = '80%';
                                modalContent.style.textAlign = 'center';
                                
                                const largeImage = document.createElement('img');
                                largeImage.src = image.src;
                                largeImage.style.maxWidth = '100%';
                                largeImage.style.maxHeight = '80vh';
                                
                                const closeBtn = document.createElement('span');
                                closeBtn.textContent = '×';
                                closeBtn.style.color = 'white';
                                closeBtn.style.position = 'absolute';
                                closeBtn.style.top = '15px';
                                closeBtn.style.right = '35px';
                                closeBtn.style.fontSize = '40px';
                                closeBtn.style.fontWeight = 'bold';
                                closeBtn.style.cursor = 'pointer';
                                
                                closeBtn.onclick = function() {
                                    document.body.removeChild(modal);
                                };
                                
                                modalContent.appendChild(largeImage);
                                modal.appendChild(closeBtn);
                                modal.appendChild(modalContent);
                                document.body.appendChild(modal);
                            });
                            
                            imageWrapper.appendChild(image);
                            imageGallery.appendChild(imageWrapper);
                        });
                        
                        imagesDiv.appendChild(imageGallery);
                        issueBody.appendChild(imagesDiv);
                    }
                    
                    issueCard.appendChild(issueHeader);
                    issueCard.appendChild(issueBody);
                    issuesContainer.appendChild(issueCard);
                });
            } else {
                alert('获取记录详情失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('获取记录详情失败: ' + error);
            console.error('获取记录详情错误:', error);
        });
        
    // 打印功能
    document.getElementById('print-btn').addEventListener('click', function() {
        window.print();
    });
});
</script>

<style>
/* 两列布局 */
.two-column-layout {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
}

.column {
    padding: 0 8px;
    box-sizing: border-box;
}

.left-column {
    flex: 0 0 40%;
}

.right-column {
    flex: 0 0 60%;
}

/* 信息行样式 */
.info-section {
    margin-bottom: 12px;
}

.section-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.info-row {
    display: flex;
    margin-bottom: 3px;
    font-size: 12px;
}

.info-label {
    width: 70px;
    text-align: right;
    padding-right: 8px;
    font-weight: 500;
    color: #555;
}

.info-value {
    flex: 1;
}

/* 卡片样式 */
.card {
    margin-bottom: 10px;
    border: 1px solid #eee;
    border-radius: 3px;
}

.card-header {
    background-color: #f8f9fa;
    padding: 6px 10px;
    border-bottom: 1px solid #eee;
}

.card-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.card-body {
    padding: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .two-column-layout {
        flex-direction: column;
    }
    
    .left-column,
    .right-column {
        flex: 0 0 100%;
    }
    
    .info-label {
        width: 80px;
    }
}

@media print {
    .page-header .btn-group {
        display: none;
    }
    
    body {
        padding: 15mm;
    }
    
    .card {
        border: none;
        break-inside: avoid;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
    }
}

.image-thumbnail:hover {
    box-shadow: 0 0 5px rgba(0,0,0,0.5);
}
</style>
{% endblock %} 