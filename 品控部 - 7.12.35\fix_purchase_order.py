#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix purchase_order column issue
"""

from db_config import get_db_connection
import traceback

def add_purchase_order_column():
    """Add purchase_order column to pending_inspections table"""
    try:
        print("Adding purchase_order column to pending_inspections table...")
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("DESCRIBE pending_inspections")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        if 'purchase_order' in column_names:
            print("✓ purchase_order column already exists")
            return True
        
        # Add the column
        cursor.execute("""
            ALTER TABLE pending_inspections 
            ADD COLUMN purchase_order VARCHAR(100) COMMENT '采购单号' AFTER supplier_name
        """)
        print("✓ Added purchase_order column")
        
        # Add index
        cursor.execute("""
            ALTER TABLE pending_inspections 
            ADD INDEX idx_purchase_order (purchase_order)
        """)
        print("✓ Added index for purchase_order")
        
        conn.commit()
        
        # Verify the column was added
        cursor.execute("DESCRIBE pending_inspections")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        if 'purchase_order' in column_names:
            print("✓ purchase_order column successfully added")
            return True
        else:
            print("✗ Failed to add purchase_order column")
            return False
            
    except Exception as e:
        print(f"✗ Error adding purchase_order column: {e}")
        traceback.print_exc()
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = add_purchase_order_column()
    if success:
        print("\n🎉 purchase_order column added successfully!")
    else:
        print("\n❌ Failed to add purchase_order column")
