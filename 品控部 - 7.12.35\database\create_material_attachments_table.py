#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建物料附件表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_config import get_db_connection

def create_material_attachments_table():
    """创建物料附件表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("创建物料附件表...")
        
        # 创建物料附件表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_id INT NOT NULL,
                file_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
                file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
                file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
                file_type VARCHAR(100) NOT NULL COMMENT '文件类型/MIME类型',
                file_extension VARCHAR(10) NOT NULL COMMENT '文件扩展名',
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
                uploaded_by VARCHAR(100) DEFAULT NULL COMMENT '上传人',
                description TEXT DEFAULT NULL COMMENT '附件描述',
                is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_material_id (material_id),
                INDEX idx_upload_time (upload_time),
                INDEX idx_is_active (is_active),
                
                FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
            COMMENT='物料附件表'
        """)
        
        print("✅ 物料附件表创建成功")
        
        # 创建上传目录
        upload_dirs = [
            'static/uploads/material_attachments',
            'static/uploads/material_attachments/documents',
            'static/uploads/material_attachments/images',
            'static/uploads/material_attachments/drawings',
            'static/uploads/material_attachments/certificates'
        ]
        
        for upload_dir in upload_dirs:
            os.makedirs(upload_dir, exist_ok=True)
            
            # 创建.gitkeep文件保持目录结构
            gitkeep_path = os.path.join(upload_dir, '.gitkeep')
            if not os.path.exists(gitkeep_path):
                with open(gitkeep_path, 'w') as f:
                    f.write('')
        
        print("✅ 上传目录创建成功")
        
        # 提交更改
        conn.commit()
        
        # 验证表创建
        cursor.execute("DESCRIBE material_attachments")
        columns = cursor.fetchall()
        print("\n📋 material_attachments表结构:")
        for column in columns:
            print(f"  - {column[0]} ({column[1]})")
        
        print("\n🎉 物料附件功能数据库准备完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始创建物料附件表")
    print("=" * 50)
    
    if create_material_attachments_table():
        print("=" * 50)
        print("🎉 物料附件功能准备完成！")
        print("\n现在可以使用以下功能：")
        print("  - 上传物料相关附件")
        print("  - 查看附件列表")
        print("  - 下载附件")
        print("  - 删除附件")
    else:
        print("❌ 创建失败，请检查错误信息")

if __name__ == "__main__":
    main()
