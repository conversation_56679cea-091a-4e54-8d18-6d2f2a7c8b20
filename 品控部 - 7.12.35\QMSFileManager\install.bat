@echo off
echo ========================================
echo QMS文件管理器安装程序
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检测到Python，正在安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo 警告: 依赖包安装可能有问题，但程序可能仍能正常运行
)

echo.
echo 正在创建桌面快捷方式...

:: 获取当前目录
set "CURRENT_DIR=%~dp0"
set "DESKTOP=%USERPROFILE%\Desktop"

:: 创建启动脚本
echo @echo off > "%CURRENT_DIR%start_qms_manager.bat"
echo cd /d "%CURRENT_DIR%" >> "%CURRENT_DIR%start_qms_manager.bat"
echo python app.py >> "%CURRENT_DIR%start_qms_manager.bat"
echo pause >> "%CURRENT_DIR%start_qms_manager.bat"

:: 创建桌面快捷方式
echo [InternetShortcut] > "%DESKTOP%\QMS文件管理器.url"
echo URL=file:///%CURRENT_DIR%start_qms_manager.bat >> "%DESKTOP%\QMS文件管理器.url"
echo IconFile=%CURRENT_DIR%icon.ico >> "%DESKTOP%\QMS文件管理器.url"

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 接下来请按照以下步骤操作：
echo.
echo 1. 双击桌面上的"QMS文件管理器"快捷方式启动程序
echo 2. 在Chrome浏览器中安装扩展程序：
echo    - 打开 chrome://extensions/
echo    - 开启"开发者模式"
echo    - 点击"加载已解压的扩展程序"
echo    - 选择 %CURRENT_DIR%browser_extension 文件夹
echo 3. 访问QMS系统，测试文件下载功能
echo.
echo 如有问题，请联系技术支持。
echo.
pause
