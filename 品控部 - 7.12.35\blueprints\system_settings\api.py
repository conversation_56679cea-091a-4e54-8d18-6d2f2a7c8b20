from flask import jsonify, request
from . import system_settings_bp
from db_config import get_db_connection
import os
import platform

@system_settings_bp.route('/api/get_setting/<setting_key>', methods=['GET'])
def get_setting(setting_key):
    """获取指定的系统设置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute("SELECT setting_value FROM system_settings WHERE setting_key = %s", (setting_key,))
        result = cursor.fetchone()

        if result:
            return jsonify({
                "success": True,
                "value": result['setting_value']
            })
        else:
            return jsonify({
                "success": False,
                "error": "设置项不存在"
            }), 404

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@system_settings_bp.route('/api/browse_drives', methods=['GET'])
def browse_drives():
    """获取系统中所有可用的磁盘驱动器"""
    try:
        drives = []

        if platform.system() == 'Windows':
            # Windows系统：获取所有驱动器
            import string
            for letter in ['C', 'D', 'E', 'F', 'G', 'H']:  # 简化，只检查常用驱动器
                drive_path = f"{letter}:\\"
                if os.path.exists(drive_path):
                    try:
                        # 获取驱动器信息
                        total, used, free = get_disk_usage(drive_path)

                        drives.append({
                            'path': drive_path,
                            'name': f"{letter}: 驱动器",
                            'type': 'fixed',
                            'total_space': total,
                            'free_space': free,
                            'used_space': used
                        })
                    except Exception as e:
                        # 如果无法访问驱动器，跳过
                        continue
        else:
            # Linux/Mac系统：从根目录开始
            drives.append({
                'path': '/',
                'name': '根目录',
                'type': 'root',
                'total_space': 0,
                'free_space': 0,
                'used_space': 0
            })

        return jsonify({
            "success": True,
            "drives": drives
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@system_settings_bp.route('/api/browse_folder', methods=['POST'])
def browse_folder():
    """浏览指定路径下的文件夹"""
    try:
        data = request.get_json()
        path = data.get('path', '')

        print(f"[DEBUG] 收到浏览文件夹请求，路径: {path}")

        if not path:
            print(f"[DEBUG] 路径为空")
            return jsonify({"success": False, "error": "路径为空"}), 400

        if not os.path.exists(path):
            print(f"[DEBUG] 路径不存在: {path}")
            return jsonify({"success": False, "error": f"路径不存在: {path}"}), 400

        if not os.path.isdir(path):
            print(f"[DEBUG] 不是有效的文件夹: {path}")
            return jsonify({"success": False, "error": f"不是有效的文件夹: {path}"}), 400

        folders = []
        files = []

        try:
            # 获取文件夹内容
            print(f"[DEBUG] 开始列出目录内容: {path}")
            items = os.listdir(path)
            print(f"[DEBUG] 找到 {len(items)} 个项目")

            for item in items:
                item_path = os.path.join(path, item)

                try:
                    if os.path.isdir(item_path):
                        # 获取文件夹信息
                        stat_info = os.stat(item_path)
                        accessible = os.access(item_path, os.R_OK)
                        folders.append({
                            'name': item,
                            'path': item_path,
                            'modified': stat_info.st_mtime,
                            'accessible': accessible
                        })
                        print(f"[DEBUG] 添加文件夹: {item} (可访问: {accessible})")
                    elif os.path.isfile(item_path):
                        # 获取文件信息（可选，用于显示）
                        stat_info = os.stat(item_path)
                        files.append({
                            'name': item,
                            'path': item_path,
                            'size': stat_info.st_size,
                            'modified': stat_info.st_mtime
                        })
                except (PermissionError, OSError) as e:
                    # 跳过无法访问的项目
                    print(f"[DEBUG] 跳过无法访问的项目: {item} - {e}")
                    continue

        except PermissionError as e:
            print(f"[DEBUG] 权限错误: {e}")
            return jsonify({"success": False, "error": "没有权限访问此文件夹"}), 403

        # 获取父目录路径
        parent_path = os.path.dirname(path) if path != os.path.dirname(path) else None

        print(f"[DEBUG] 处理完成 - 文件夹: {len(folders)}, 文件: {len(files)}, 父路径: {parent_path}")

        result = {
            "success": True,
            "current_path": path,
            "parent_path": parent_path,
            "folders": sorted(folders, key=lambda x: x['name'].lower()),
            "files": sorted(files, key=lambda x: x['name'].lower())[:50]  # 限制文件数量
        }

        print(f"[DEBUG] 返回结果: {len(result['folders'])} 个文件夹")
        return jsonify(result)

    except Exception as e:
        print(f"[DEBUG] 异常错误: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def get_disk_usage(path):
    """获取磁盘使用情况"""
    try:
        import shutil
        total, used, free = shutil.disk_usage(path)
        return total, used, free
    except Exception as e:
        print(f"获取磁盘使用情况失败: {e}")
        return 0, 0, 0

def get_drive_type(path):
    """获取驱动器类型"""
    try:
        if platform.system() == 'Windows':
            try:
                import win32file
                drive_type = win32file.GetDriveType(path)
                type_map = {
                    0: 'unknown',
                    1: 'invalid',
                    2: 'removable',
                    3: 'fixed',
                    4: 'remote',
                    5: 'cdrom',
                    6: 'ramdisk'
                }
                return type_map.get(drive_type, 'unknown')
            except ImportError:
                # 如果没有win32file，使用简单的判断
                if path.upper().startswith('C:'):
                    return 'fixed'
                elif path.upper() in ['A:', 'B:']:
                    return 'removable'
                else:
                    return 'fixed'
        else:
            return 'fixed'
    except:
        return 'unknown'

@system_settings_bp.route('/api/update_setting', methods=['POST'])
def update_setting():
    """更新系统设置"""
    try:
        data = request.get_json()
        setting_key = data.get('setting_key')
        setting_value = data.get('setting_value')
        description = data.get('description', '')

        if not setting_key:
            return jsonify({"success": False, "error": "设置键不能为空"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO system_settings (setting_key, setting_value, description)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE
            setting_value = VALUES(setting_value),
            description = VALUES(description),
            updated_at = CURRENT_TIMESTAMP
        """, (setting_key, setting_value, description))

        conn.commit()

        return jsonify({"success": True, "message": "设置更新成功"})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@system_settings_bp.route('/api/get_download_path', methods=['GET'])
def get_download_path():
    """获取文件下载路径设置"""
    try:
        # 首先尝试创建表（如果不存在）
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查表是否存在，如果不存在则创建
        cursor.execute("SHOW TABLES LIKE 'system_settings'")
        if not cursor.fetchone():
            cursor.execute("""
                CREATE TABLE system_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    description VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)
            conn.commit()

        # 现在查询设置
        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT setting_value FROM system_settings WHERE setting_key = 'file_download_path'")
        result = cursor.fetchone()

        if result:
            download_path = result['setting_value']
        else:
            # 默认路径
            download_path = "C:\\QMS1\\" if platform.system() == 'Windows' else "/opt/QMS1/"

            # 插入默认设置
            cursor.execute("""
                INSERT INTO system_settings (setting_key, setting_value, description)
                VALUES ('file_download_path', %s, '文件下载存储路径')
            """, (download_path,))
            conn.commit()

        return jsonify({
            "success": True,
            "download_path": download_path
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@system_settings_bp.route('/api/set_download_path', methods=['POST'])
def set_download_path():
    """设置文件下载路径"""
    try:
        data = request.get_json()
        download_path = data.get('download_path')

        if not download_path:
            return jsonify({"success": False, "error": "下载路径不能为空"}), 400

        # 验证路径格式
        if platform.system() == 'Windows':
            if not (download_path.endswith('\\') or download_path.endswith('/')):
                download_path += '\\'
        else:
            if not download_path.endswith('/'):
                download_path += '/'

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO system_settings (setting_key, setting_value, description)
            VALUES ('file_download_path', %s, '文件下载存储路径')
            ON DUPLICATE KEY UPDATE
            setting_value = VALUES(setting_value),
            updated_at = CURRENT_TIMESTAMP
        """, (download_path,))

        conn.commit()

        return jsonify({"success": True, "message": "下载路径设置成功", "download_path": download_path})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@system_settings_bp.route('/api/create_download_folder', methods=['POST'])
def create_download_folder():
    """创建下载文件夹"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path:
            return jsonify({"success": False, "error": "文件夹路径不能为空"}), 400

        # 创建文件夹
        os.makedirs(folder_path, exist_ok=True)

        # 验证文件夹是否创建成功
        if os.path.exists(folder_path) and os.path.isdir(folder_path):
            return jsonify({"success": True, "message": f"文件夹创建成功: {folder_path}"})
        else:
            return jsonify({"success": False, "error": "文件夹创建失败"}), 500

    except Exception as e:
        return jsonify({"success": False, "error": f"创建文件夹失败: {str(e)}"}), 500
