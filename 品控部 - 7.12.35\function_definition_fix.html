<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数未定义错误修复 - 品质中心管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .error-section {
            margin: 30px 0;
            padding: 20px;
            background: #fff5f5;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }
        .fix-section {
            margin: 30px 0;
            padding: 20px;
            background: #f0fff4;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .error-title {
            color: #dc3545;
        }
        .fix-title {
            color: #28a745;
        }
        .code-block {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .code-block h5 {
            margin-top: 0;
            color: #2d3748;
            font-family: inherit;
        }
        .error-code {
            background: #f8d7da;
            color: #721c24;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .fixed-code {
            background: #d4edda;
            color: #155724;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .highlight-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e2e8f0;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 20px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #1976d2;
        }
        .timeline-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        .timeline-desc {
            color: #4a5568;
            font-size: 14px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        .before-card {
            background: #fff5f5;
            border-left: 4px solid #dc3545;
        }
        .after-card {
            background: #f0fff4;
            border-left: 4px solid #28a745;
        }
        .card-title {
            font-weight: 600;
            margin-bottom: 10px;
        }
        .before-title {
            color: #dc3545;
        }
        .after-title {
            color: #28a745;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 函数未定义错误修复</h1>
        <p>解决批量导入页面中"fetchMaterialInfo is not defined"的JavaScript错误</p>

        <div class="error-section">
            <div class="section-title error-title">❌ 错误详情</div>
            <div class="code-block">
                <h5>控制台错误信息：</h5>
                <pre><span class="error-code">batch_import_sampling:909 Uncaught ReferenceError: fetchMaterialInfo is not defined
    at HTMLInputElement.onblur (batch_import_sampling:909:122)</span></pre>
            </div>
            
            <h4>问题原因分析：</h4>
            <ul>
                <li><strong>函数定义时机问题：</strong>HTML中的内联事件处理器在页面加载时就需要函数存在</li>
                <li><strong>JavaScript加载顺序：</strong>JavaScript代码在页面底部，而HTML中的onblur事件在页面顶部</li>
                <li><strong>作用域问题：</strong>内联事件处理器无法访问到后面定义的函数</li>
            </ul>
        </div>

        <div class="highlight-box">
            <h3>🔍 问题发生的时间线</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-title">1. 页面开始加载</div>
                    <div class="timeline-desc">浏览器开始解析HTML文档</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-title">2. 遇到input元素</div>
                    <div class="timeline-desc">HTML中的 &lt;input onblur="fetchMaterialInfo(this)"&gt; 被解析</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-title">3. 尝试绑定事件</div>
                    <div class="timeline-desc">浏览器尝试查找fetchMaterialInfo函数，但此时函数还未定义</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-title">4. 用户触发事件</div>
                    <div class="timeline-desc">用户在料号输入框失去焦点时，触发onblur事件</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-title">5. 错误发生</div>
                    <div class="timeline-desc">浏览器报错：fetchMaterialInfo is not defined</div>
                </div>
            </div>
        </div>

        <div class="comparison-grid">
            <div class="comparison-card before-card">
                <div class="card-title before-title">修复前：内联事件处理器</div>
                <div class="code-block">
                    <h5>HTML:</h5>
                    <pre><span class="error-code">&lt;input type="text" class="material-code" 
       placeholder="输入料号" 
       onblur="fetchMaterialInfo(this)"&gt;</span></pre>
                    
                    <h5>JavaScript (在页面底部):</h5>
                    <pre>async function fetchMaterialInfo(input) {
    // 函数定义在这里，但HTML已经尝试引用了
}</pre>
                </div>
                <p style="color: #dc3545; font-size: 12px;">❌ 函数定义晚于HTML引用，导致未定义错误</p>
            </div>
            
            <div class="comparison-card after-card">
                <div class="card-title after-title">修复后：动态事件绑定</div>
                <div class="code-block">
                    <h5>HTML:</h5>
                    <pre><span class="fixed-code">&lt;input type="text" class="material-code" 
       placeholder="输入料号"&gt;</span></pre>
                    
                    <h5>JavaScript:</h5>
                    <pre>// DOM加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    bindMaterialCodeEvents();
});

function bindMaterialCodeEvents() {
    const inputs = document.querySelectorAll('.material-code');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            fetchMaterialInfo(this);
        });
    });
}</pre>
                </div>
                <p style="color: #28a745; font-size: 12px;">✅ 在DOM加载完成后动态绑定事件，确保函数已定义</p>
            </div>
        </div>

        <div class="fix-section">
            <div class="section-title fix-title">✅ 修复方案</div>
            
            <h4>1. 移除内联事件处理器</h4>
            <div class="code-block">
                <h5>修复前：</h5>
                <pre><span class="error-code">&lt;input type="text" class="material-code" placeholder="输入料号" onblur="fetchMaterialInfo(this)"&gt;</span></pre>
                
                <h5>修复后：</h5>
                <pre><span class="fixed-code">&lt;input type="text" class="material-code" placeholder="输入料号"&gt;</span></pre>
            </div>

            <h4>2. 添加事件绑定函数</h4>
            <div class="code-block">
                <h5>新增函数：</h5>
                <pre><span class="fixed-code">function bindMaterialCodeEvents() {
    // 绑定所有料号输入框的事件
    const materialCodeInputs = document.querySelectorAll('.material-code');
    materialCodeInputs.forEach(input => {
        input.addEventListener('blur', function() {
            fetchMaterialInfo(this);
        });
    });
    console.log('已绑定', materialCodeInputs.length, '个料号输入框的事件');
}</span></pre>
            </div>

            <h4>3. 在初始化时调用绑定函数</h4>
            <div class="code-block">
                <h5>修改initializeEventListeners函数：</h5>
                <pre>function initializeEventListeners() {
    // ... 其他事件绑定 ...
    
    <span class="fixed-code">// 绑定料号输入框的事件
    bindMaterialCodeEvents();</span>
    
    // ... 其他事件绑定 ...
}</pre>
            </div>

            <h4>4. 修复动态添加行的事件绑定</h4>
            <div class="code-block">
                <h5>修复addRow函数：</h5>
                <pre>function addRow() {
    // ... 创建新行 ...
    
    <span class="fixed-code">// 重新绑定料号输入框的事件
    const materialCodeInput = newRow.querySelector('.material-code');
    if (materialCodeInput) {
        materialCodeInput.addEventListener('blur', function() {
            fetchMaterialInfo(this);
        });
    }</span>
    
    tbody.appendChild(newRow);
}</pre>
            </div>

            <h4>5. 修复清空数据后的事件绑定</h4>
            <div class="code-block">
                <h5>修复clearData函数：</h5>
                <pre>function clearData() {
    if (currentMethod === 'manual') {
        tbody.innerHTML = `...`; // 重新创建HTML
        <span class="fixed-code">// 重新绑定事件
        bindMaterialCodeEvents();</span>
    }
}</pre>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 修复效果</h3>
            
            <h4>技术改进：</h4>
            <ul>
                <li><strong>消除时序问题：</strong>确保函数在使用前已经定义</li>
                <li><strong>动态事件绑定：</strong>使用addEventListener替代内联事件处理器</li>
                <li><strong>统一事件管理：</strong>所有事件绑定都在JavaScript中统一管理</li>
                <li><strong>支持动态内容：</strong>新添加的行也能正确绑定事件</li>
            </ul>
            
            <h4>用户体验提升：</h4>
            <ul>
                <li><strong>无JavaScript错误：</strong>控制台不再出现函数未定义的错误</li>
                <li><strong>功能正常工作：</strong>输入料号后能正确触发自动获取功能</li>
                <li><strong>动态行支持：</strong>新添加的行也支持自动获取功能</li>
                <li><strong>清空后恢复：</strong>清空数据后重新创建的行也能正常工作</li>
            </ul>
            
            <h4>最佳实践：</h4>
            <ul>
                <li><strong>避免内联事件：</strong>不在HTML中使用内联事件处理器</li>
                <li><strong>DOM就绪后绑定：</strong>在DOMContentLoaded事件后绑定所有事件</li>
                <li><strong>统一事件管理：</strong>使用专门的函数管理事件绑定</li>
                <li><strong>动态内容处理：</strong>为动态添加的内容重新绑定事件</li>
            </ul>
        </div>

        <div class="fix-section">
            <div class="section-title fix-title">✅ 修复完成</div>
            <p><strong>fetchMaterialInfo函数未定义错误已完全修复！</strong></p>
            
            <p>现在的功能特点：</p>
            <ul>
                <li>✅ <strong>无JavaScript错误：</strong>控制台不再报错</li>
                <li>✅ <strong>事件正确绑定：</strong>所有料号输入框都能触发自动获取</li>
                <li>✅ <strong>动态行支持：</strong>新添加的行也支持自动获取功能</li>
                <li>✅ <strong>清空后恢复：</strong>清空数据后功能依然正常</li>
                <li>✅ <strong>代码更规范：</strong>遵循现代JavaScript最佳实践</li>
            </ul>
            
            <p>用户现在可以正常使用批量导入页面的物料信息自动获取功能了！</p>
        </div>
    </div>
</body>
</html>
