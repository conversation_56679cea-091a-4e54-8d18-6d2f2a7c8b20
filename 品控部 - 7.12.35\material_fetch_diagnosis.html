<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料信息自动获取诊断 - 品质中心管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .diagnosis-section {
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #1976d2;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 15px;
        }
        .check-item {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        .check-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2d3748;
        }
        .check-desc {
            color: #4a5568;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .check-steps {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .highlight-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        .problem-box {
            background: #fff5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .solution-box {
            background: #f0fff4;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .code-block {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .api-test {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            margin: 15px 0;
        }
        .api-url {
            background: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 5px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success {
            background: #28a745;
        }
        .status-warning {
            background: #ffc107;
        }
        .status-error {
            background: #dc3545;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            margin: 8px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #1976d2;
        }
        .checklist li::before {
            content: "☐ ";
            font-weight: bold;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 物料信息自动获取诊断工具</h1>
        <p>帮助诊断批量导入页面中物料信息无法自动获取的问题</p>

        <div class="problem-box">
            <h3>❌ 问题描述</h3>
            <p>在批量导入待检页面中，输入料号后无法自动获取物料名称、规格、供应商信息。</p>
        </div>

        <div class="diagnosis-section">
            <div class="section-title">🔍 问题诊断步骤</div>
            
            <div class="check-item">
                <div class="check-title">1. 检查浏览器控制台</div>
                <div class="check-desc">打开浏览器开发者工具，查看控制台是否有错误信息</div>
                <div class="check-steps">
                    1. 按F12打开开发者工具
                    2. 切换到"Console"标签
                    3. 在料号输入框中输入料号并失去焦点
                    4. 查看控制台输出的调试信息
                </div>
                <p><strong>期望看到的调试信息：</strong></p>
                <div class="code-block">
🔍 开始获取物料信息: MT001
📡 请求物料信息URL: /api/material_info/MT001
📡 物料信息响应状态: 200
📋 物料信息响应数据: {success: true, material: {...}}
✅ 物料信息获取成功: {...}
✅ 物料基本信息已填充
📡 请求供应商信息URL: /incoming/api/recent_supplier/MT001
📡 供应商信息响应状态: 200
📋 供应商信息响应数据: {success: true, supplier: "ABC电子"}
✅ 供应商信息已填充: ABC电子
                </div>
            </div>

            <div class="check-item">
                <div class="check-title">2. 检查数据库连接</div>
                <div class="check-desc">确认数据库连接正常，materials表中有数据</div>
                <div class="check-steps">
                    # 在项目根目录运行测试脚本
                    python test_material_api.py
                </div>
                <p><strong>期望结果：</strong>数据库连接成功，materials表中有物料数据</p>
            </div>

            <div class="check-item">
                <div class="check-title">3. 手动测试API端点</div>
                <div class="check-desc">直接访问API端点，检查返回数据</div>
                <div class="api-test">
                    <p><strong>物料信息API测试：</strong></p>
                    <div class="api-url">GET http://localhost:5000/api/material_info/ABC001</div>
                    <p>期望返回：</p>
                    <div class="code-block">
{
  "success": true,
  "material": {
    "material_number": "ABC001",
    "material_name": "铝合金板",
    "specification": "100x50x2mm",
    "material_type": "6061铝合金",
    "color": "银色"
  }
}
                    </div>
                </div>
                <div class="api-test">
                    <p><strong>供应商信息API测试：</strong></p>
                    <div class="api-url">GET http://localhost:5000/incoming/api/recent_supplier/ABC001</div>
                    <p>期望返回：</p>
                    <div class="code-block">
{
  "success": true,
  "supplier": "某某供应商",
  "inspection_date": "2024-12-01"
}
                    </div>
                    <p>或者（如果没有历史记录）：</p>
                    <div class="code-block">
{
  "success": false,
  "message": "未找到该物料的历史供应商信息"
}
                    </div>
                </div>
            </div>

            <div class="check-item">
                <div class="check-title">4. 检查Flask应用状态</div>
                <div class="check-desc">确认Flask应用正在运行且端口正确</div>
                <div class="check-steps">
                    1. 确认Flask应用在5000端口运行
                    2. 访问 http://localhost:5000 确认应用可访问
                    3. 检查应用日志是否有错误信息
                </div>
            </div>

            <div class="check-item">
                <div class="check-title">5. 检查事件绑定</div>
                <div class="check-desc">确认料号输入框的blur事件正确绑定</div>
                <div class="check-steps">
                    1. 在浏览器控制台中运行：
                       document.querySelectorAll('.material-code').length
                    2. 应该返回料号输入框的数量
                    3. 检查是否有"已绑定 X 个料号输入框的事件"的日志
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🔧 常见问题和解决方案</h3>
            
            <h4>问题1：控制台显示"fetchMaterialInfo is not defined"</h4>
            <p><strong>原因：</strong>函数定义时机问题</p>
            <p><strong>解决：</strong>已修复，使用动态事件绑定替代内联事件处理器</p>
            
            <h4>问题2：API返回404错误</h4>
            <p><strong>原因：</strong>API端点不存在或路径错误</p>
            <p><strong>解决：</strong>检查Flask应用是否正确注册了蓝图和路由</p>
            
            <h4>问题3：API返回500错误</h4>
            <p><strong>原因：</strong>数据库连接失败或SQL查询错误</p>
            <p><strong>解决：</strong>检查数据库连接配置和materials表结构</p>
            
            <h4>问题4：返回数据为空</h4>
            <p><strong>原因：</strong>materials表中没有对应的物料数据</p>
            <p><strong>解决：</strong>运行数据生成脚本添加测试数据</p>
        </div>

        <div class="solution-box">
            <h3>✅ 解决方案检查清单</h3>
            <ul class="checklist">
                <li>确认Flask应用正在运行（python app.py）</li>
                <li>确认数据库连接正常（python test_material_api.py）</li>
                <li>确认materials表中有数据</li>
                <li>确认API端点可以正常访问</li>
                <li>确认浏览器控制台没有JavaScript错误</li>
                <li>确认料号输入框的事件正确绑定</li>
                <li>确认网络连接正常，没有防火墙阻止</li>
            </ul>
        </div>

        <div class="diagnosis-section">
            <div class="section-title">🛠️ 快速修复步骤</div>
            
            <h4>步骤1：重新生成测试数据</h4>
            <div class="code-block">
# 在项目根目录运行
python database/generate_sample_data.py
            </div>
            
            <h4>步骤2：重启Flask应用</h4>
            <div class="code-block">
# 停止当前应用（Ctrl+C）
# 重新启动
python app.py
            </div>
            
            <h4>步骤3：清除浏览器缓存</h4>
            <div class="code-block">
# 按Ctrl+Shift+R强制刷新页面
# 或者按F12 -> Network -> Disable cache
            </div>
            
            <h4>步骤4：测试功能</h4>
            <div class="code-block">
1. 打开批量导入页面
2. 按F12打开控制台
3. 在料号输入框输入：ABC001
4. 点击其他地方使输入框失去焦点
5. 查看控制台调试信息和字段是否自动填充
            </div>
        </div>

        <div class="highlight-box">
            <h3>📞 获取帮助</h3>
            <p>如果按照上述步骤仍然无法解决问题，请：</p>
            <ol>
                <li>截图浏览器控制台的错误信息</li>
                <li>运行 <code>python test_material_api.py</code> 并截图结果</li>
                <li>提供具体的错误描述和复现步骤</li>
            </ol>
        </div>
    </div>
</body>
</html>
