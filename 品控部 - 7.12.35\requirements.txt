# ===================================
# 品质中心管理系统 - 依赖包配置
# ===================================

# Web框架核心
Flask==2.3.3
Werkzeug==2.3.7

# 数据库连接
mysql-connector-python==8.2.0

# 开发和调试工具
python-dotenv==1.0.0

# ===================================
# 可选依赖（根据需要安装）
# ===================================

# 如果需要更好的日期处理：
# python-dateutil==2.8.2

# 如果需要数据验证：
# marshmallow==3.20.1

# 如果需要更好的日志：
# colorlog==6.7.0

# 如果需要Excel文件处理：
# openpyxl==3.1.2
# xlrd==2.0.1

# 如果需要PDF处理：
# PyPDF2==3.0.1

# 如果需要图像处理：
# Pillow==10.0.1