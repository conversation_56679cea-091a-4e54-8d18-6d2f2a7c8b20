{% extends "base.html" %}

{% block title %}智能批量导入 - 品质中心管理系统{% endblock %}

{% block content %}
<div class="page-header">
    <h1>智能批量导入待检物料</h1>
    <p class="page-description">根据物料预设检验类型自动分流到抽样检验、全部检验或免检</p>
</div>

<div class="import-container">
    <!-- 导入说明 -->
    <div class="import-info">
        <h3>📋 导入说明</h3>
        <ul>
            <li><strong>智能分流</strong>：系统会根据物料管理中预设的检验类型自动分流</li>
            <li><strong>抽样检验</strong>：物料设置为"抽样"的进入抽样检验待检清单</li>
            <li><strong>全部检验</strong>：物料设置为"全检"的进入全部检验待检清单</li>
            <li><strong>免检处理</strong>：物料设置为"免检"的直接生成检验记录</li>
            <li><strong>默认处理</strong>：未设置检验类型的物料默认为抽样检验</li>
        </ul>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section">
        <h3>📁 文件上传</h3>
        <div class="upload-area" id="upload-area">
            <div class="upload-content">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>点击选择Excel文件或拖拽文件到此处</p>
                <p class="upload-hint">支持 .xlsx 和 .xls 格式</p>
            </div>
            <input type="file" id="file-input" accept=".xlsx,.xls" style="display: none;">
        </div>
        
        <div class="upload-actions">
            <button type="button" class="btn btn-secondary" onclick="downloadTemplate()">
                <i class="fas fa-download"></i> 下载模板
            </button>
            <button type="button" class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                <i class="fas fa-file-excel"></i> 选择文件
            </button>
        </div>
    </div>

    <!-- 手动输入区域 -->
    <div class="manual-input-section">
        <h3>✏️ 手动输入</h3>
        <div class="manual-input-controls">
            <button type="button" class="btn btn-success" onclick="addRow()">
                <i class="fas fa-plus"></i> 添加行
            </button>
            <button type="button" class="btn btn-warning" onclick="clearAll()">
                <i class="fas fa-trash"></i> 清空所有
            </button>
        </div>

        <div class="table-container">
            <table class="manual-input-table" id="manual-input-table">
                <thead>
                    <tr>
                        <th>物料料号 *</th>
                        <th>物料名称</th>
                        <th>规格</th>
                        <th>供应商</th>
                        <th>采购单号</th>
                        <th>来料数量 *</th>
                        <th>单位</th>
                        <th>批次号</th>
                        <th>到货日期</th>
                        <th>预设检验类型</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="manual-input-tbody">
                    <!-- 动态生成的行 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 预览和提交区域 -->
    <div class="submit-section">
        <div class="submit-actions">
            <button type="button" class="btn btn-info" onclick="previewData()">
                <i class="fas fa-eye"></i> 预览数据
            </button>
            <button type="button" class="btn btn-success" onclick="submitData()">
                <i class="fas fa-check"></i> 智能导入
            </button>
            <button type="button" class="btn btn-secondary" onclick="goBack()">
                <i class="fas fa-arrow-left"></i> 返回
            </button>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div id="preview-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>数据预览</h3>
            <span class="close" onclick="closePreviewModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="preview-summary"></div>
            <div id="preview-content"></div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closePreviewModal()">关闭</button>
            <button type="button" class="btn btn-success" onclick="confirmSubmit()">确认导入</button>
        </div>
    </div>
</div>

<!-- 结果模态框 -->
<div id="result-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>导入结果</h3>
            <span class="close" onclick="closeResultModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="result-content"></div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-primary" onclick="viewResults()">查看结果</button>
            <button type="button" class="btn btn-secondary" onclick="closeResultModal()">关闭</button>
        </div>
    </div>
</div>

<style>
.import-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.import-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.import-info h3 {
    color: #495057;
    margin-bottom: 15px;
}

.import-info ul {
    margin: 0;
    padding-left: 20px;
}

.import-info li {
    margin-bottom: 8px;
    color: #6c757d;
}

.upload-section, .manual-input-section {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.upload-area {
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #f8f9fa;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #0056b3;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.upload-content i {
    font-size: 48px;
    color: #007bff;
    margin-bottom: 15px;
}

.upload-content p {
    margin: 10px 0;
    color: #495057;
}

.upload-hint {
    font-size: 14px;
    color: #6c757d;
}

.upload-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.manual-input-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
}

.table-container {
    overflow-x: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.manual-input-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1000px;
}

.manual-input-table th,
.manual-input-table td {
    padding: 12px;
    border: 1px solid #dee2e6;
    text-align: left;
}

.manual-input-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.manual-input-table input,
.manual-input-table select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.manual-input-table input:focus,
.manual-input-table select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.inspection-type-display {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.inspection-type-sampling {
    background: #d1ecf1;
    color: #0c5460;
}

.inspection-type-full {
    background: #d4edda;
    color: #155724;
}

.inspection-type-exempt {
    background: #fff3cd;
    color: #856404;
}

.submit-section {
    text-align: center;
    padding: 30px;
}

.submit-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #117a8b;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.success {
    background: #28a745;
}

.toast.error {
    background: #dc3545;
}

.toast.warning {
    background: #ffc107;
    color: #212529;
}

.toast.info {
    background: #17a2b8;
}
</style>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    // 添加一行默认数据
    addRow();

    // 设置文件上传事件
    setupFileUpload();

    // 设置拖拽上传
    setupDragAndDrop();
}

function setupFileUpload() {
    const fileInput = document.getElementById('file-input');
    fileInput.addEventListener('change', handleFileSelect);
}

function setupDragAndDrop() {
    const uploadArea = document.getElementById('upload-area');

    uploadArea.addEventListener('click', () => {
        document.getElementById('file-input').click();
    });

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect({ target: { files: files } });
        }
    });
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.match(/\.(xlsx|xls)$/)) {
        showToast('请选择Excel文件', 'error');
        return;
    }

    showToast('文件上传成功，正在解析...', 'info');

    // 这里可以添加Excel文件解析逻辑
    // 暂时显示成功消息
    setTimeout(() => {
        showToast('文件解析完成，请检查数据', 'success');
    }, 1000);
}

function addRow() {
    const tbody = document.getElementById('manual-input-tbody');
    const rowCount = tbody.children.length;

    const row = document.createElement('tr');
    row.innerHTML = `
        <td><input type="text" class="material-code" placeholder="输入料号" onblur="fetchMaterialInfo(this)"></td>
        <td><input type="text" class="material-name" placeholder="物料名称"></td>
        <td><input type="text" class="specification" placeholder="规格"></td>
        <td><input type="text" class="supplier" placeholder="供应商"></td>
        <td><input type="text" class="purchase-order" placeholder="采购单号"></td>
        <td><input type="number" class="quantity" placeholder="数量" min="1"></td>
        <td><input type="text" class="unit" placeholder="单位" value="PCS"></td>
        <td><input type="text" class="batch-number" placeholder="批次号"></td>
        <td><input type="date" class="arrival-date" value="${new Date().toISOString().split('T')[0]}"></td>
        <td><span class="inspection-type-display">未设置</span></td>
        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeRow(this)"><i class="fas fa-trash"></i></button></td>
    `;

    tbody.appendChild(row);
}

function removeRow(button) {
    const row = button.closest('tr');
    row.remove();
}

function clearAll() {
    if (confirm('确定要清空所有数据吗？')) {
        document.getElementById('manual-input-tbody').innerHTML = '';
        addRow(); // 添加一行空行
    }
}

async function fetchMaterialInfo(input) {
    const materialCode = input.value.trim();
    if (!materialCode) return;

    const row = input.closest('tr');

    try {
        const response = await fetch(`/material_management/api/material/${materialCode}`);
        const data = await response.json();

        if (data.success && data.data) {
            const material = data.data;

            // 填充物料信息
            row.querySelector('.material-name').value = material.material_name || '';
            row.querySelector('.specification').value = material.specification || '';
            row.querySelector('.unit').value = material.unit || 'PCS';

            // 显示检验类型
            const inspectionTypeDisplay = row.querySelector('.inspection-type-display');
            const inspectionType = material.inspection_type || '抽样';

            inspectionTypeDisplay.textContent = inspectionType;
            inspectionTypeDisplay.className = 'inspection-type-display';

            if (inspectionType === '抽样') {
                inspectionTypeDisplay.classList.add('inspection-type-sampling');
            } else if (inspectionType === '全检' || inspectionType === '全部') {
                inspectionTypeDisplay.classList.add('inspection-type-full');
            } else if (inspectionType === '免检') {
                inspectionTypeDisplay.classList.add('inspection-type-exempt');
            } else {
                inspectionTypeDisplay.classList.add('inspection-type-sampling');
            }

            showToast(`物料 ${materialCode} 信息已自动填充`, 'success');
        } else {
            showToast(`物料 ${materialCode} 不存在，请手动填写信息`, 'warning');
        }
    } catch (error) {
        console.error('获取物料信息失败:', error);
        showToast('获取物料信息失败', 'error');
    }
}

function collectTableData() {
    const tbody = document.getElementById('manual-input-tbody');
    const rows = tbody.querySelectorAll('tr');
    const data = [];

    rows.forEach(row => {
        const materialCode = row.querySelector('.material-code').value.trim();
        const quantity = row.querySelector('.quantity').value.trim();

        if (materialCode && quantity) {
            data.push({
                material_code: materialCode,
                material_name: row.querySelector('.material-name').value.trim(),
                specification: row.querySelector('.specification').value.trim(),
                supplier_name: row.querySelector('.supplier').value.trim(),
                purchase_order: row.querySelector('.purchase-order').value.trim(),
                incoming_quantity: parseFloat(quantity),
                unit: row.querySelector('.unit').value.trim() || 'PCS',
                batch_number: row.querySelector('.batch-number').value.trim(),
                arrival_date: row.querySelector('.arrival-date').value
            });
        }
    });

    return data;
}

function previewData() {
    const data = collectTableData();

    if (data.length === 0) {
        showToast('请先输入数据', 'warning');
        return;
    }

    // 显示预览模态框
    const modal = document.getElementById('preview-modal');
    const summaryDiv = document.getElementById('preview-summary');
    const contentDiv = document.getElementById('preview-content');

    summaryDiv.innerHTML = `
        <div class="preview-summary">
            <h4>导入概览</h4>
            <p>共 <strong>${data.length}</strong> 个物料待导入</p>
            <p>系统将根据物料预设检验类型自动分流到相应的检验清单</p>
        </div>
    `;

    let tableHtml = `
        <table class="preview-table">
            <thead>
                <tr>
                    <th>料号</th>
                    <th>名称</th>
                    <th>数量</th>
                    <th>预设检验类型</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.forEach(item => {
        tableHtml += `
            <tr>
                <td>${item.material_code}</td>
                <td>${item.material_name}</td>
                <td>${item.incoming_quantity} ${item.unit}</td>
                <td><span class="inspection-type-badge">待确认</span></td>
            </tr>
        `;
    });

    tableHtml += '</tbody></table>';
    contentDiv.innerHTML = tableHtml;

    modal.style.display = 'block';
}

async function submitData() {
    const data = collectTableData();

    if (data.length === 0) {
        showToast('请先输入数据', 'warning');
        return;
    }

    if (!confirm(`确认要导入 ${data.length} 个物料吗？系统将根据预设检验类型自动分流。`)) {
        return;
    }

    try {
        showToast('正在导入，请稍候...', 'info');

        const response = await fetch('/pending_inspection/api/pending_inspections/smart_batch_import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                materials: data,
                batch_name: `智能导入_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`
            })
        });

        const result = await response.json();

        if (result.success) {
            showImportResult(result);
        } else {
            showToast(`导入失败：${result.error}`, 'error');
        }
    } catch (error) {
        console.error('导入失败:', error);
        showToast('导入失败，请重试', 'error');
    }
}

function showImportResult(result) {
    const modal = document.getElementById('result-modal');
    const contentDiv = document.getElementById('result-content');

    const data = result.data;

    contentDiv.innerHTML = `
        <div class="import-result">
            <div class="result-summary">
                <h4>✅ 导入完成</h4>
                <p>${result.message}</p>
            </div>

            <div class="result-details">
                <div class="result-item">
                    <span class="result-label">抽样检验：</span>
                    <span class="result-value">${data.sampling_count} 个物料</span>
                </div>
                <div class="result-item">
                    <span class="result-label">全部检验：</span>
                    <span class="result-value">${data.full_count} 个物料</span>
                </div>
                <div class="result-item">
                    <span class="result-label">免检处理：</span>
                    <span class="result-value">${data.exempt_count} 个物料</span>
                </div>
                ${data.error_count > 0 ? `
                <div class="result-item error">
                    <span class="result-label">导入失败：</span>
                    <span class="result-value">${data.error_count} 个物料</span>
                </div>
                ` : ''}
            </div>

            ${data.exempt_records && data.exempt_records.length > 0 ? `
            <div class="exempt-records">
                <h5>免检记录</h5>
                <ul>
                    ${data.exempt_records.map(record =>
                        `<li>${record.material_code} - ${record.material_name} (${record.report_code})</li>`
                    ).join('')}
                </ul>
            </div>
            ` : ''}
        </div>
    `;

    modal.style.display = 'block';
    showToast('导入完成！', 'success');
}

function confirmSubmit() {
    closePreviewModal();
    submitData();
}

function closePreviewModal() {
    document.getElementById('preview-modal').style.display = 'none';
}

function closeResultModal() {
    document.getElementById('result-modal').style.display = 'none';
}

function viewResults() {
    // 跳转到相应的待检清单页面
    window.location.href = '/incoming/pending_list';
}

function goBack() {
    window.history.back();
}

function downloadTemplate() {
    showToast('模板下载功能开发中', 'warning');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
