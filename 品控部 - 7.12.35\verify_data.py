#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的数据
"""

from db_config import get_db_connection

def verify_data():
    """验证生成的数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 检查待检清单数据
        cursor.execute('SELECT COUNT(*) as count FROM pending_inspections')
        pending_count = cursor.fetchone()['count']
        print(f'待检清单记录数: {pending_count}')
        
        # 按状态统计
        cursor.execute('SELECT status, COUNT(*) as count FROM pending_inspections GROUP BY status')
        status_stats = cursor.fetchall()
        print('待检清单状态分布:')
        for stat in status_stats:
            print(f'  {stat["status"]}: {stat["count"]}条')
        
        # 检查检验记录数据
        cursor.execute('SELECT COUNT(*) as count FROM sampling_inspection')
        inspection_count = cursor.fetchone()['count']
        print(f'\n检验记录数: {inspection_count}')
        
        # 检查最近的几条记录
        cursor.execute('SELECT material_number, material_name, supplier, inspection_date FROM sampling_inspection ORDER BY created_at DESC LIMIT 5')
        recent_records = cursor.fetchall()
        print('\n最近5条检验记录:')
        for record in recent_records:
            print(f'  {record["material_number"]} - {record["material_name"]} - {record["supplier"]} - {record["inspection_date"]}')
        
        # 检查批次信息
        cursor.execute('SELECT COUNT(*) as count FROM pending_inspection_batches')
        batch_count = cursor.fetchone()['count']
        print(f'\n批次数: {batch_count}')
        
        if batch_count > 0:
            cursor.execute('SELECT batch_name, total_items, pending_items, in_progress_items, completed_items FROM pending_inspection_batches ORDER BY created_at DESC LIMIT 1')
            batch_info = cursor.fetchone()
            print('最新批次信息:')
            print(f'  批次名称: {batch_info["batch_name"]}')
            print(f'  总物料数: {batch_info["total_items"]}')
            print(f'  待检数量: {batch_info["pending_items"]}')
            print(f'  检验中数量: {batch_info["in_progress_items"]}')
            print(f'  已完成数量: {batch_info["completed_items"]}')
        
        cursor.close()
        conn.close()
        
        print('\n✅ 数据验证完成！')
        
    except Exception as e:
        print(f'❌ 错误: {e}')

if __name__ == "__main__":
    verify_data()
