{% extends "base.html" %}

{% block title %}编辑物料 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 页面整体布局 - 参考抽样检验样式 */
    .material-form-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 8px;
        background: #fff;
        border-radius: 3px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* 主要内容区域 - 两列布局 */
    .main-content-wrapper {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 8px;
        margin-bottom: 8px;
    }

    /* 表单区域样式 - 紧凑型 */
    .form-section {
        background: #fafafa;
        border-radius: 2px;
        padding: 6px;
        border: 1px solid #e0e0e0;
        margin-bottom: 8px;
    }

    .form-section-title {
        font-size: 13px;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 4px;
        padding-bottom: 2px;
        border-bottom: 2px solid #1976d2;
        display: flex;
        align-items: center;
    }

    .form-section-title i {
        margin-right: 4px;
        font-size: 14px;
    }

    /* 表单组样式 - 紧凑型 */
    .form-group {
        margin-bottom: 5px;
        display: flex;
        align-items: center;
    }

    .form-label {
        width: 70px;
        text-align: right;
        padding-right: 8px;
        flex-shrink: 0;
        font-size: 11px;
        font-weight: 500;
        color: #555;
    }

    .form-label.required::after {
        content: " *";
        color: #f44336;
    }

    .form-input {
        flex: 1;
        height: 32px;
        padding: 2px 8px;
        font-size: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.2s ease;
        box-sizing: border-box;
        max-width: 180px;
    }

    .form-input:focus {
        outline: none;
        border-color: #1976d2;
        box-shadow: 0 0 0 1px rgba(25, 118, 210, 0.2);
    }

    .form-textarea {
        height: 60px;
        resize: vertical;
        max-width: none;
    }

    .form-help {
        font-size: 10px;
        color: #666;
        margin-left: 8px;
        flex-shrink: 0;
    }

    /* 按钮样式 - 参考抽样检验 */
    .btn {
        height: 32px;
        line-height: 30px;
        padding: 0 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 12px;
        transition: all 0.2s;
        vertical-align: middle;
        box-sizing: border-box;
        border: none;
        cursor: pointer;
        text-decoration: none;
        min-width: 80px;
    }

    .btn-primary {
        background: #1976d2;
        color: white;
    }

    .btn-primary:hover {
        background: #1565c0;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
    }

    .btn-group {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #eee;
    }

    /* 浮动提示信息样式 */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        pointer-events: none;
    }

    .toast {
        background: white;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: 8px;
        padding: 12px 16px;
        min-width: 300px;
        max-width: 400px;
        font-size: 13px;
        border-left: 4px solid;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        pointer-events: auto;
        position: relative;
    }

    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }

    .toast.success {
        border-left-color: #28a745;
        color: #155724;
    }

    .toast.success .toast-icon {
        color: #28a745;
    }

    .toast.error {
        border-left-color: #dc3545;
        color: #721c24;
    }

    .toast.error .toast-icon {
        color: #dc3545;
    }

    .toast.info {
        border-left-color: #17a2b8;
        color: #0c5460;
    }

    .toast.info .toast-icon {
        color: #17a2b8;
    }

    .toast-content {
        display: flex;
        align-items: flex-start;
        gap: 8px;
    }

    .toast-icon {
        font-size: 16px;
        margin-top: 1px;
        flex-shrink: 0;
    }

    .toast-message {
        flex: 1;
        line-height: 1.4;
    }

    .toast-close {
        position: absolute;
        top: 8px;
        right: 8px;
        background: none;
        border: none;
        font-size: 16px;
        color: #999;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        transition: all 0.2s ease;
    }

    .toast-close:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #666;
    }

    /* 两列表单布局 */
    .form-columns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    /* 全宽表单组 */
    .form-group-full {
        grid-column: 1 / -1;
    }
    
    /* 附件上传样式 - 紧凑型 */
    .attachment-upload-area {
        margin-bottom: 8px;
    }

    .upload-zone {
        border: 1px dashed #ddd;
        border-radius: 3px;
        padding: 12px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        min-height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .upload-zone:hover {
        border-color: #1976d2;
        background: #f0f7ff;
    }

    .upload-zone.dragover {
        border-color: #1976d2;
        background: #e3f2fd;
    }

    .upload-content {
        text-align: center;
    }

    .upload-content i {
        color: #1976d2;
        margin-bottom: 4px;
        display: block;
    }

    .upload-content p {
        margin: 2px 0;
        color: #666;
        line-height: 1.2;
    }

    .upload-hint {
        color: #999 !important;
    }

    /* 附件列表样式 - 紧凑型 */
    .attachments-list {
        margin-top: 8px;
    }

    .attachments-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
        padding-bottom: 4px;
        border-bottom: 1px solid #eee;
    }

    .attachments-count {
        font-size: 10px;
        color: #666;
        background: #f0f0f0;
        padding: 1px 6px;
        border-radius: 8px;
    }

    .attachments-container {
        max-height: 200px;
        overflow-y: auto;
    }

    .attachment-item {
        display: flex;
        align-items: center;
        padding: 4px 6px;
        border: 1px solid #eee;
        border-radius: 3px;
        margin-bottom: 4px;
        background: #fff;
        transition: all 0.2s ease;
    }

    .attachment-item:hover {
        border-color: #1976d2;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .attachment-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        border-radius: 3px;
        margin-right: 6px;
        font-size: 12px;
    }

    .attachment-info {
        flex: 1;
        min-width: 0;
    }

    .attachment-name {
        font-size: 11px;
        font-weight: 500;
        color: #333;
        margin-bottom: 1px;
        word-break: break-all;
        line-height: 1.2;
    }

    .attachment-meta {
        font-size: 9px;
        color: #666;
        line-height: 1.1;
    }

    .attachment-actions {
        display: flex;
        gap: 2px;
    }

    .attachment-btn {
        padding: 2px 4px;
        font-size: 9px;
        border: none;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 30px;
    }

    .attachment-btn.download {
        background: #e3f2fd;
        color: #1976d2;
    }

    .attachment-btn.download:hover {
        background: #1976d2;
        color: white;
    }

    .attachment-btn.delete {
        background: #ffebee;
        color: #f44336;
    }

    .attachment-btn.delete:hover {
        background: #f44336;
        color: white;
    }

    .upload-progress {
        margin-top: 10px;
        padding: 10px;
        background: #f0f7ff;
        border-radius: 4px;
        display: none;
    }

    .progress-bar {
        width: 100%;
        height: 6px;
        background: #e0e0e0;
        border-radius: 3px;
        overflow: hidden;
        margin-top: 5px;
    }

    .progress-fill {
        height: 100%;
        background: #1976d2;
        transition: width 0.3s ease;
        width: 0%;
    }

    /* 下载进度条样式 */
    .download-progress-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .download-progress-dialog {
        background: white;
        border-radius: 8px;
        padding: 20px;
        min-width: 350px;
        max-width: 450px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        animation: fadeInScale 0.3s ease-out;
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    .download-progress-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .download-progress-file {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
        word-break: break-all;
        background: #f8f9fa;
        padding: 8px 12px;
        border-radius: 4px;
        border-left: 3px solid #1976d2;
    }

    .download-progress-bar {
        width: 100%;
        height: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 12px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .download-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #1976d2, #42a5f5, #1976d2);
        background-size: 200% 100%;
        border-radius: 6px;
        transition: width 0.3s ease;
        width: 0%;
        animation: progressShimmer 2s infinite;
    }

    @keyframes progressShimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    .download-progress-text {
        font-size: 13px;
        color: #666;
        text-align: center;
        margin-bottom: 15px;
    }

    .download-progress-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
    }

    .download-progress-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #666;
    }

    .download-progress-status.success {
        color: #4caf50;
    }

    .download-progress-status.error {
        color: #f44336;
    }

    .download-progress-btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
    }

    .download-progress-btn.cancel {
        background: #f5f5f5;
        color: #666;
    }

    .download-progress-btn.cancel:hover {
        background: #e0e0e0;
    }

    .download-progress-btn.open {
        background: #1976d2;
        color: white;
    }

    .download-progress-btn.open:hover {
        background: #1565c0;
    }

    .download-progress-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 响应式样式 */
    @media (max-width: 1200px) {
        .main-content-wrapper {
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .right-column {
            order: -1;
        }
    }

    @media (max-width: 768px) {
        .material-form-container {
            padding: 4px;
        }

        .form-columns {
            grid-template-columns: 1fr;
        }

        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }

        .form-label {
            width: auto;
            text-align: left;
            padding-right: 0;
            margin-bottom: 2px;
        }

        .form-input {
            max-width: none;
            width: 100%;
        }

        .attachment-item {
            flex-direction: column;
            align-items: flex-start;
            padding: 6px;
        }

        .attachment-icon {
            margin-bottom: 4px;
            margin-right: 0;
        }

        .attachment-actions {
            width: 100%;
            justify-content: flex-end;
            margin-top: 4px;
        }

        .btn-group {
            flex-direction: column;
            gap: 4px;
        }

        .btn {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="material-form-container">
    <div class="page-header">
        <h1 style="font-size: 16px; margin: 0 0 8px 0;">编辑物料信息</h1>
    </div>

    <div class="main-content-wrapper">
        <!-- 左侧：基本信息表单 -->
        <div class="left-column">
            <form id="material-form">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-info-circle"></i>
                        基本信息
                    </div>

                    <div class="form-group">
                        <label class="form-label required" for="material_number">料号</label>
                        <input type="text" id="material_number" class="form-input" value="{{ material.material_number }}" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label required" for="material_name">名称</label>
                        <input type="text" id="material_name" class="form-input" value="{{ material.material_name }}" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="specification">规格</label>
                        <input type="text" id="specification" class="form-input" value="{{ material.specification or '' }}">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="material_type">材质</label>
                        <input type="text" id="material_type" class="form-input" value="{{ material.material_type or '' }}">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="color">颜色</label>
                        <input type="text" id="color" class="form-input" value="{{ material.color or '' }}">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="unit">单位</label>
                        <input type="text" id="unit" class="form-input" value="{{ material.unit or '' }}">
                    </div>
                </div>

                <!-- 分类信息 -->
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-tags"></i>
                        分类信息
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="material_category">物料类型</label>
                        <select id="material_category" class="form-input">
                            <option value="">请选择</option>
                            <option value="原材料" {{ 'selected' if material.material_category == '原材料' else '' }}>原材料</option>
                            <option value="半成品" {{ 'selected' if material.material_category == '半成品' else '' }}>半成品</option>
                            <option value="成品" {{ 'selected' if material.material_category == '成品' else '' }}>成品</option>
                            <option value="辅料" {{ 'selected' if material.material_category == '辅料' else '' }}>辅料</option>
                            <option value="包装材料" {{ 'selected' if material.material_category == '包装材料' else '' }}>包装材料</option>
                            <option value="工具" {{ 'selected' if material.material_category == '工具' else '' }}>工具</option>
                            <option value="其他" {{ 'selected' if material.material_category == '其他' else '' }}>其他</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="inspection_type">检验类型</label>
                        <select id="inspection_type" class="form-input">
                            <option value="">请选择</option>
                            <option value="抽样" {{ 'selected' if material.inspection_type == '抽样' else '' }}>抽样检验</option>
                            <option value="全检" {{ 'selected' if material.inspection_type == '全检' else '' }}>全部检验</option>
                            <option value="免检" {{ 'selected' if material.inspection_type == '免检' else '' }}>免检</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="supplier">供应商</label>
                        <input type="text" id="supplier" class="form-input" value="{{ material.supplier or '' }}">
                    </div>
                </div>

                <!-- 备注信息 -->
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-comment"></i>
                        备注信息
                    </div>

                    <div class="form-group form-group-full">
                        <label class="form-label" for="remarks">备注</label>
                        <textarea id="remarks" class="form-input form-textarea" placeholder="请输入备注信息">{{ material.remarks or '' }}</textarea>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="btn-group">
                    <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存
                    </button>
                </div>
            </form>
        </div>


        <!-- 右侧：附件管理 -->
        <div class="right-column">
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-paperclip"></i>
                    附件管理
                </div>

                <!-- 附件上传区域 -->
                <div class="attachment-upload-area">
                    <div class="upload-zone" id="upload-zone">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 24px;"></i>
                            <p style="font-size: 11px; margin: 4px 0;">点击或拖拽上传</p>
                            <p class="upload-hint" style="font-size: 10px;">支持图片、文档、图纸等</p>
                        </div>
                    </div>
                    <input type="file" id="file-input" multiple accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.dwg,.dxf,.txt" style="display: none;">

                    <div class="form-group" style="margin-top: 8px;">
                        <label class="form-label" for="attachment-description">描述</label>
                        <input type="text" id="attachment-description" class="form-input" placeholder="附件描述（可选）">
                    </div>
                </div>

                <!-- 现有附件列表 -->
                <div class="attachments-list" id="attachments-list">
                    <div class="attachments-header">
                        <h4 style="font-size: 12px; margin: 0;">现有附件</h4>
                        <span class="attachments-count" id="attachments-count">加载中...</span>
                    </div>
                    <div class="attachments-container" id="attachments-container">
                        <!-- 附件列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
            <a href="{{ url_for('material_management.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存修改
            </button>
        </div>
    </form>
</div>

<!-- 浮动提示容器 -->
<div class="toast-container" id="toast-container"></div>

<script>
    document.getElementById('material-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 获取表单数据
        const formData = {
            material_number: document.getElementById('material_number').value.trim(),
            material_name: document.getElementById('material_name').value.trim(),
            specification: document.getElementById('specification').value.trim(),
            material_type: document.getElementById('material_type').value.trim(),
            color: document.getElementById('color').value.trim(),
            description: document.getElementById('description').value.trim()
        };
        
        // 验证必填字段
        if (!formData.material_number) {
            showAlert('请输入物料料号', 'error');
            document.getElementById('material_number').focus();
            return;
        }
        
        if (!formData.material_name) {
            showAlert('请输入物料名称', 'error');
            document.getElementById('material_name').focus();
            return;
        }
        
        // 禁用提交按钮
        const submitBtn = document.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        
        // 获取物料类型和检验类型
        const materialCategory = document.getElementById('material_category').value;
        const inspectionType = document.getElementById('inspection_type').value;

        // 添加新字段到formData
        formData.material_category = materialCategory;
        formData.inspection_type = inspectionType;

        // 发送请求
        fetch('/material_management/api/material/{{ material.id }}', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('物料更新成功！', 'success');
                // 延迟跳转，让用户看到成功提示
                setTimeout(() => {
                    window.location.href = '{{ url_for("material_management.index") }}';
                }, 1500);
            } else {
                showAlert('更新失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showAlert('更新失败: ' + error, 'error');
            console.error('更新错误:', error);
        })
        .finally(() => {
            // 恢复提交按钮
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save"></i> 保存修改';
        });
    });
    
    // 显示浮动提示信息
    function showAlert(message, type) {
        const container = document.getElementById('toast-container');

        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        // 确定图标
        let icon = '';
        switch(type) {
            case 'success':
                icon = 'fas fa-check-circle';
                break;
            case 'error':
                icon = 'fas fa-exclamation-circle';
                break;
            case 'info':
                icon = 'fas fa-info-circle';
                break;
            default:
                icon = 'fas fa-info-circle';
        }

        // 创建toast内容
        toast.innerHTML = `
            <div class="toast-content">
                <i class="toast-icon ${icon}"></i>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="removeToast(this.parentElement)">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加到容器
        container.appendChild(toast);

        // 触发显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 自动移除
        setTimeout(() => {
            removeToast(toast);
        }, 5000);
    }

    // 移除toast
    function removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    // 附件管理功能
    document.addEventListener('DOMContentLoaded', function() {
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const attachmentDescription = document.getElementById('attachment-description');

        // 加载现有附件
        loadAttachments();

        // 文件拖拽上传
        uploadZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFiles(files);
            }
        });

        // 点击上传 - 防止重复触发
        uploadZone.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            fileInput.click();
        });

        fileInput.addEventListener('change', function(e) {
            if (this.files.length > 0) {
                handleFiles(this.files);
                // 清空文件输入，允许重复选择同一文件
                this.value = '';
            }
        });
    });

    // 处理文件上传
    function handleFiles(files) {
        for (let file of files) {
            uploadFile(file);
        }
    }

    // 上传单个文件
    function uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('material_id', '{{ material.id }}');
        formData.append('description', document.getElementById('attachment-description').value);

        // 显示上传进度
        showUploadProgress(file.name);

        fetch('/material_management/api/material/{{ material.id }}/attachments', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideUploadProgress();
            if (data.success) {
                showAlert('文件上传成功！', 'success');
                loadAttachments(); // 重新加载附件列表
                document.getElementById('attachment-description').value = ''; // 清空描述
            } else {
                showAlert('上传失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            hideUploadProgress();
            showAlert('上传失败: ' + error, 'error');
        });
    }

    // 加载附件列表
    function loadAttachments() {
        fetch('/material_management/api/material/{{ material.id }}/attachments')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAttachments(data.attachments);
                updateAttachmentsCount(data.attachments.length);
            } else {
                showAlert('加载附件失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showAlert('加载附件失败: ' + error, 'error');
        });
    }

    // 显示附件列表
    function displayAttachments(attachments) {
        const container = document.getElementById('attachments-container');

        if (attachments.length === 0) {
            container.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无附件</div>';
            return;
        }

        container.innerHTML = attachments.map(attachment => `
            <div class="attachment-item" data-attachment-id="${attachment.id}" data-file-name="${attachment.file_name}" data-file-extension="${attachment.file_extension}">
                <div class="attachment-icon">
                    <i class="fas ${getFileIcon(attachment.file_extension)}"></i>
                </div>
                <div class="attachment-info">
                    <div class="attachment-name">${attachment.file_name}</div>
                    <div class="attachment-meta">
                        ${formatFileSize(attachment.file_size)} • ${formatDate(attachment.upload_time)}
                        ${attachment.description ? ' • ' + attachment.description : ''}
                    </div>
                </div>
                <div class="attachment-actions">
                    <button class="attachment-btn download" onclick="openAttachment(${attachment.id})">
                        <i class="fas fa-external-link-alt"></i> 打开
                    </button>
                    <button class="attachment-btn delete" onclick="deleteAttachment(${attachment.id})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 更新附件数量
    function updateAttachmentsCount(count) {
        document.getElementById('attachments-count').textContent = `共 ${count} 个附件`;
    }

    // 获取文件图标
    function getFileIcon(extension) {
        const iconMap = {
            'pdf': 'fa-file-pdf',
            'doc': 'fa-file-word', 'docx': 'fa-file-word',
            'xls': 'fa-file-excel', 'xlsx': 'fa-file-excel',
            'jpg': 'fa-file-image', 'jpeg': 'fa-file-image', 'png': 'fa-file-image', 'gif': 'fa-file-image',
            'dwg': 'fa-drafting-compass', 'dxf': 'fa-drafting-compass',
            'txt': 'fa-file-alt'
        };
        return iconMap[extension.toLowerCase()] || 'fa-file';
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // 格式化日期
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    // 显示上传进度
    function showUploadProgress(fileName) {
        const progressHtml = `
            <div class="upload-progress" id="upload-progress">
                <div>正在上传: ${fileName}</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        `;
        document.querySelector('.attachment-upload-area').insertAdjacentHTML('beforeend', progressHtml);
        document.getElementById('upload-progress').style.display = 'block';
    }

    // 隐藏上传进度
    function hideUploadProgress() {
        const progress = document.getElementById('upload-progress');
        if (progress) {
            progress.remove();
        }
    }

    // 打开附件
    function openAttachment(attachmentId) {
        // 先获取附件信息，判断文件类型
        const attachmentUrl = `/material_management/api/material/{{ material.id }}/attachments/${attachmentId}/download`;

        // 从页面中查找附件信息
        const attachmentElement = document.querySelector(`[data-attachment-id="${attachmentId}"]`);
        if (attachmentElement) {
            const fileName = attachmentElement.dataset.fileName;
            const fileExtension = attachmentElement.dataset.fileExtension;

            handleAttachmentOpen({
                file_name: fileName,
                file_extension: fileExtension
            }, attachmentUrl);
        } else {
            // 如果找不到附件信息，获取附件列表
            fetch(`/material_management/api/material/{{ material.id }}/attachments`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const attachment = data.attachments.find(att => att.id === attachmentId);
                    if (attachment) {
                        handleAttachmentOpen(attachment, attachmentUrl);
                    } else {
                        // 如果找不到附件信息，直接打开
                        window.open(attachmentUrl, '_blank', 'noopener,noreferrer');
                    }
                } else {
                    // 获取失败，直接打开
                    window.open(attachmentUrl, '_blank', 'noopener,noreferrer');
                }
            })
            .catch(error => {
                console.error('获取附件信息失败:', error);
                // 出错时直接打开
                window.open(attachmentUrl, '_blank', 'noopener,noreferrer');
            });
        }
    }

    // 处理附件打开逻辑
    function handleAttachmentOpen(attachment, url) {
        // 调试信息
        console.log('Attachment data:', attachment);

        const fileExtension = attachment.file_extension ? attachment.file_extension.toLowerCase() : '';

        // 可以在浏览器中直接预览的文件类型
        const inlineTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'txt', 'html', 'htm', 'xml', 'json'];

        if (inlineTypes.includes(fileExtension)) {
            // 直接在浏览器中打开预览
            window.open(url, '_blank', 'noopener,noreferrer');
        } else {
            // 其他文件类型直接下载到客户端
            downloadFileToClient(url, attachment.file_name);
        }
    }

    // 新函数：直接下载文件到客户端
    function downloadFileToClient(url, fileName) {
        try {
            // 创建一个临时的下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName; // 设置下载文件名
            link.style.display = 'none';

            // 添加到页面并触发点击
            document.body.appendChild(link);
            link.click();

            // 清理
            document.body.removeChild(link);

            // 显示提示信息
            showAlert(`文件 "${fileName}" 开始下载到您的设备`, 'success');

        } catch (error) {
            console.error('下载失败:', error);
            showAlert('文件下载失败，请重试', 'error');
        }
    }

    // 保留原有函数以兼容（现在也使用客户端下载）
    function downloadAndOpenFile(url, fileName, fileExtension) {
        // 现在也使用客户端下载
        downloadFileToClient(url, fileName);
    }

    // 获取下载路径并下载文件
    function getDownloadPathAndDownload(sourceUrl, fileName, fileExtension) {
        // 首先获取系统设置的下载路径
        fetch('/system_settings/api/get_download_path')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const localPath = data.download_path;
                // 下载文件到本地路径
                downloadToLocalPath(sourceUrl, fileName, fileExtension, localPath);
            } else {
                // 使用默认路径
                const defaultPath = 'C:\\QMS1\\';
                downloadToLocalPath(sourceUrl, fileName, fileExtension, defaultPath);
            }
        })
        .catch(error => {
            console.error('获取下载路径失败:', error);
            // 使用默认路径
            const defaultPath = 'C:\\QMS1\\';
            downloadToLocalPath(sourceUrl, fileName, fileExtension, defaultPath);
        });
    }

    // 下载文件到本地路径
    function downloadToLocalPath(sourceUrl, fileName, fileExtension, localPath) {
        updateDownloadProgress(10, '正在准备下载...');

        // 从URL中提取material_id和attachment_id
        const urlParts = sourceUrl.split('/');
        const materialId = urlParts[urlParts.indexOf('material') + 1];
        const attachmentId = urlParts[urlParts.indexOf('attachments') + 1];

        // 调用后端API下载文件到本地路径
        fetch(`/material_management/api/material/${materialId}/attachments/${attachmentId}/download_to_local`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                local_path: localPath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDownloadProgress(100, '下载完成！');
                updateDownloadStatus('success', '下载完成');

                // 启用自动打开按钮
                const openBtn = document.getElementById('open-file-btn');
                if (openBtn) {
                    openBtn.disabled = false;
                    openBtn.textContent = '自动打开';
                    openBtn.style.background = '#4caf50';
                }

                // 2秒后自动打开文件
                setTimeout(() => {
                    autoOpenLocalFile(data.local_file_path, fileName, fileExtension);
                }, 2000);
            } else {
                updateDownloadProgress(0, '下载失败！');
                updateDownloadStatus('error', '下载失败');

                setTimeout(() => {
                    closeDownloadProgress();
                    showAlert('文件下载失败：' + data.error, 'error');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('下载失败:', error);
            updateDownloadProgress(0, '下载失败！');
            updateDownloadStatus('error', '下载失败');

            setTimeout(() => {
                closeDownloadProgress();
                showAlert('文件下载失败，请重试', 'error');
            }, 2000);
        });
    }

    // 自动打开本地文件
    function autoOpenLocalFile(filePath, fileName, fileExtension) {
        updateDownloadStatus('success', '正在打开文件...');

        // 调用后端API打开本地文件
        fetch('/material_management/api/open_local_file', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_path: filePath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                setTimeout(() => {
                    closeDownloadProgress();
                    showAlert(`文件 "${fileName}" 已自动打开！`, 'success');

                    // 显示打开建议
                    const openSuggestion = getOpenSuggestion(fileExtension);
                    if (openSuggestion) {
                        setTimeout(() => {
                            showAlert(openSuggestion, 'info');
                        }, 1500);
                    }
                }, 1000);
            } else {
                setTimeout(() => {
                    closeDownloadProgress();
                    showAlert('文件打开失败：' + data.error, 'error');
                    showManualOpenInstructions(fileName, fileExtension, filePath);
                }, 1000);
            }
        })
        .catch(error => {
            console.error('打开文件失败:', error);
            setTimeout(() => {
                closeDownloadProgress();
                showAlert('文件打开失败，请手动打开', 'error');
                showManualOpenInstructions(fileName, fileExtension, filePath);
            }, 1000);
        });
    }

    // 显示下载进度对话框
    function showDownloadProgressDialog(fileName, fileExtension) {
        const overlay = document.createElement('div');
        overlay.className = 'download-progress-overlay';
        overlay.id = 'download-progress-overlay';

        overlay.innerHTML = `
            <div class="download-progress-dialog">
                <div class="download-progress-title">
                    <i class="fas fa-download"></i>
                    正在下载文件
                </div>
                <div class="download-progress-file">${fileName}</div>
                <div class="download-progress-bar">
                    <div class="download-progress-fill" id="download-progress-fill"></div>
                </div>
                <div class="download-progress-text" id="download-progress-text">准备下载...</div>
                <div class="download-progress-actions">
                    <div class="download-progress-status" id="download-progress-status">
                        <i class="fas fa-spinner fa-spin"></i>
                        下载中...
                    </div>
                    <div>
                        <button class="download-progress-btn cancel" onclick="cancelDownload()">取消</button>
                        <button class="download-progress-btn open" id="open-file-btn" onclick="openDownloadedFile('${fileName}', '${fileExtension}')" disabled>自动打开</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    // 使用fetch下载文件并显示进度
    function downloadFileWithProgress(url, fileName, fileExtension) {
        fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('下载失败');
            }

            const contentLength = response.headers.get('content-length');
            const total = parseInt(contentLength, 10);
            let loaded = 0;

            const reader = response.body.getReader();
            const chunks = [];

            function pump() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        // 下载完成
                        const blob = new Blob(chunks);
                        const downloadUrl = window.URL.createObjectURL(blob);

                        // 创建下载链接
                        const downloadLink = document.createElement('a');
                        downloadLink.href = downloadUrl;
                        downloadLink.download = fileName;
                        downloadLink.style.display = 'none';

                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);

                        // 清理URL对象
                        window.URL.revokeObjectURL(downloadUrl);

                        // 更新进度条为完成状态
                        updateDownloadProgress(100, '下载完成！');
                        updateDownloadStatus('success', '下载完成');

                        // 启用自动打开按钮
                        const openBtn = document.getElementById('open-file-btn');
                        if (openBtn) {
                            openBtn.disabled = false;
                            openBtn.textContent = '自动打开';
                            openBtn.style.background = '#4caf50';
                        }

                        // 2秒后自动打开文件
                        setTimeout(() => {
                            autoOpenFile(fileName, fileExtension, url);
                        }, 2000);

                        return;
                    }

                    chunks.push(value);
                    loaded += value.length;

                    if (total) {
                        const progress = Math.round((loaded / total) * 100);
                        updateDownloadProgress(progress, `已下载 ${formatFileSize(loaded)} / ${formatFileSize(total)}`);
                    } else {
                        updateDownloadProgress(null, `已下载 ${formatFileSize(loaded)}`);
                    }

                    return pump();
                });
            }

            return pump();
        })
        .catch(error => {
            console.error('下载失败:', error);
            updateDownloadProgress(0, '下载失败！');
            updateDownloadStatus('error', '下载失败');

            // 显示错误提示
            setTimeout(() => {
                closeDownloadProgress();
                showAlert('文件下载失败，请重试', 'error');
            }, 2000);
        });
    }

    // 更新下载进度
    function updateDownloadProgress(progress, text) {
        const progressFill = document.getElementById('download-progress-fill');
        const progressText = document.getElementById('download-progress-text');

        if (progressFill && progress !== null) {
            progressFill.style.width = progress + '%';
        }

        if (progressText) {
            progressText.textContent = text;
        }
    }

    // 更新下载状态
    function updateDownloadStatus(type, text) {
        const statusElement = document.getElementById('download-progress-status');
        if (statusElement) {
            statusElement.className = `download-progress-status ${type}`;
            if (type === 'success') {
                statusElement.innerHTML = `<i class="fas fa-check-circle"></i> ${text}`;
            } else if (type === 'error') {
                statusElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${text}`;
            } else {
                statusElement.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
            }
        }
    }

    // 自动打开文件
    function autoOpenFile(fileName, fileExtension, originalUrl) {
        updateDownloadStatus('success', '正在打开文件...');

        const extension = fileExtension.toLowerCase();

        // 尝试多种方式静默打开文件
        attemptSilentFileOpen(fileName, fileExtension, originalUrl);
    }

    // 尝试静默打开文件（不打开新界面）
    function attemptSilentFileOpen(fileName, fileExtension, originalUrl) {
        const extension = fileExtension.toLowerCase();

        // 方法1: 尝试使用应用程序协议
        if (tryApplicationProtocolOpen(fileName, extension)) {
            setTimeout(() => {
                closeDownloadProgress();
                showAlert(`文件 "${fileName}" 正在用相应软件打开...`, 'success');
            }, 1000);
            return;
        }

        // 方法2: 对于PDF等文件，创建隐藏iframe预览
        if (['pdf'].includes(extension)) {
            if (tryIframePreview(originalUrl, fileName)) {
                setTimeout(() => {
                    closeDownloadProgress();
                    showAlert(`文件 "${fileName}" 已在后台打开！`, 'success');
                }, 1000);
                return;
            }
        }

        // 方法3: 尝试使用系统默认程序
        if (trySystemDefaultOpen(fileName, extension)) {
            setTimeout(() => {
                closeDownloadProgress();
                showAlert(`文件 "${fileName}" 正在用默认程序打开...`, 'success');
            }, 1000);
            return;
        }

        // 所有方法都失败，显示手动指导
        setTimeout(() => {
            closeDownloadProgress();
            showManualOpenInstructions(fileName, fileExtension);
        }, 1000);
    }

    // 尝试使用应用程序协议打开
    function tryApplicationProtocolOpen(fileName, extension) {
        try {
            const protocolMap = {
                'xlsx': 'ms-excel:ofe|u|',
                'xls': 'ms-excel:ofe|u|',
                'docx': 'ms-word:ofe|u|',
                'doc': 'ms-word:ofe|u|',
                'pptx': 'ms-powerpoint:ofe|u|',
                'ppt': 'ms-powerpoint:ofe|u|'
            };

            if (protocolMap[extension]) {
                // 构建下载路径
                const downloadsPath = navigator.userAgent.includes('Windows') ?
                    'C:\\Users\\<USER>\\Downloads\\' : '/Users/<USER>/Downloads/';

                const protocolUrl = protocolMap[extension] + downloadsPath + fileName;

                // 创建隐藏的iframe来触发协议
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = protocolUrl;
                document.body.appendChild(iframe);

                // 3秒后移除iframe
                setTimeout(() => {
                    if (iframe.parentNode) {
                        iframe.parentNode.removeChild(iframe);
                    }
                }, 3000);

                return true;
            }
        } catch (e) {
            console.log('应用程序协议方法失败:', e);
        }
        return false;
    }

    // 尝试使用iframe预览
    function tryIframePreview(originalUrl, fileName) {
        try {
            // 创建一个浮动的iframe来预览PDF
            const iframe = document.createElement('iframe');
            iframe.src = originalUrl;
            iframe.style.position = 'fixed';
            iframe.style.top = '50px';
            iframe.style.right = '20px';
            iframe.style.width = '400px';
            iframe.style.height = '500px';
            iframe.style.border = '1px solid #ccc';
            iframe.style.borderRadius = '4px';
            iframe.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            iframe.style.zIndex = '9999';
            iframe.style.background = 'white';

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.position = 'absolute';
            closeBtn.style.top = '55px';
            closeBtn.style.right = '25px';
            closeBtn.style.width = '25px';
            closeBtn.style.height = '25px';
            closeBtn.style.border = 'none';
            closeBtn.style.background = '#f44336';
            closeBtn.style.color = 'white';
            closeBtn.style.borderRadius = '50%';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.fontSize = '14px';
            closeBtn.style.zIndex = '10000';

            closeBtn.onclick = function() {
                if (iframe.parentNode) iframe.parentNode.removeChild(iframe);
                if (closeBtn.parentNode) closeBtn.parentNode.removeChild(closeBtn);
            };

            document.body.appendChild(iframe);
            document.body.appendChild(closeBtn);

            // 10秒后自动关闭
            setTimeout(() => {
                if (iframe.parentNode) iframe.parentNode.removeChild(iframe);
                if (closeBtn.parentNode) closeBtn.parentNode.removeChild(closeBtn);
            }, 10000);

            return true;
        } catch (e) {
            console.log('iframe预览方法失败:', e);
            return false;
        }
    }

    // 尝试使用系统默认程序打开
    function trySystemDefaultOpen(fileName, extension) {
        try {
            // 对于Windows系统，尝试使用shell执行
            if (navigator.userAgent.includes('Windows')) {
                // 尝试使用ActiveX（仅在IE或特殊配置的浏览器中工作）
                if (window.ActiveXObject || "ActiveXObject" in window) {
                    const shell = new ActiveXObject("WScript.Shell");
                    const downloadsPath = shell.ExpandEnvironmentStrings("%USERPROFILE%\\Downloads\\");
                    shell.Run('"' + downloadsPath + fileName + '"');
                    return true;
                }
            }
        } catch (e) {
            console.log('系统默认程序方法失败:', e);
        }
        return false;
    }

    // 尝试打开已下载文件的多种方式
    function attemptToOpenDownloadedFile(fileName, fileExtension) {
        const extension = fileExtension.toLowerCase();

        // 方法1: 尝试使用Windows shell命令（需要特殊权限）
        if (window.navigator.platform.indexOf('Win') !== -1) {
            try {
                // 尝试使用ActiveX对象（仅在IE或启用ActiveX的浏览器中工作）
                if (window.ActiveXObject || "ActiveXObject" in window) {
                    const shell = new ActiveXObject("WScript.Shell");
                    const downloadsPath = shell.ExpandEnvironmentStrings("%USERPROFILE%\\Downloads\\");
                    shell.Run(`"${downloadsPath}${fileName}"`);
                    return true;
                }
            } catch (e) {
                console.log('ActiveX方法失败:', e);
            }
        }

        // 方法2: 使用自定义协议（如果已注册）
        try {
            const protocolMap = {
                'xlsx': 'ms-excel:',
                'xls': 'ms-excel:',
                'docx': 'ms-word:',
                'doc': 'ms-word:',
                'pptx': 'ms-powerpoint:',
                'ppt': 'ms-powerpoint:',
                'pdf': 'ms-edge:'
            };

            if (protocolMap[extension]) {
                const downloadsPath = getDownloadsPath();
                window.location.href = `${protocolMap[extension]}ofe|u|${downloadsPath}${fileName}`;
                return true;
            }
        } catch (e) {
            console.log('协议方法失败:', e);
        }

        return false; // 所有方法都失败
    }

    // 获取下载文件夹路径
    function getDownloadsPath() {
        const userAgent = navigator.userAgent;
        if (userAgent.indexOf('Windows') !== -1) {
            return 'file:///C:/Users/' + (process.env.USERNAME || 'User') + '/Downloads/';
        } else if (userAgent.indexOf('Mac') !== -1) {
            return 'file:///Users/' + (process.env.USER || 'user') + '/Downloads/';
        } else {
            return 'file:///home/' + (process.env.USER || 'user') + '/Downloads/';
        }
    }

    // 显示手动打开指导
    function showManualOpenInstructions(fileName, fileExtension, filePath) {
        const instructions = getDetailedOpenInstructions(fileExtension);

        // 创建详细的指导对话框
        const overlay = document.createElement('div');
        overlay.className = 'download-progress-overlay';
        overlay.id = 'manual-open-overlay';

        overlay.innerHTML = `
            <div class="download-progress-dialog" style="max-width: 500px;">
                <div class="download-progress-title">
                    <i class="fas fa-info-circle" style="color: #2196f3;"></i>
                    文件下载完成
                </div>
                <div class="download-progress-file">${fileName}</div>
                <div style="padding: 15px 0; font-size: 14px; line-height: 1.6; color: #333;">
                    <p><strong>文件已下载到本地QMS文件夹中。</strong></p>
                    <p><strong>文件位置：</strong></p>
                    <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 5px 0;">
                        ${filePath || 'C:\\QMS1\\' + fileName}
                    </div>
                    <p><strong>打开方式：</strong></p>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li>打开文件管理器</li>
                        <li>导航到上述文件路径</li>
                        <li>双击文件"${fileName}"即可打开</li>
                    </ol>
                    <p style="background: #f0f7ff; padding: 10px; border-radius: 4px; border-left: 3px solid #2196f3;">
                        <i class="fas fa-lightbulb"></i> <strong>建议：</strong>${instructions}
                    </p>
                </div>
                <div class="download-progress-actions" style="justify-content: center;">
                    <button class="download-progress-btn open" onclick="closeManualInstructions()" style="background: #4caf50;">
                        <i class="fas fa-check"></i> 我知道了
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    // 获取详细的打开指导
    function getDetailedOpenInstructions(fileExtension) {
        const instructions = {
            'xlsx': '使用 Microsoft Excel、WPS表格 或 Google Sheets 打开',
            'xls': '使用 Microsoft Excel、WPS表格 或 LibreOffice Calc 打开',
            'docx': '使用 Microsoft Word、WPS文字 或 Google Docs 打开',
            'doc': '使用 Microsoft Word、WPS文字 或 LibreOffice Writer 打开',
            'pptx': '使用 Microsoft PowerPoint、WPS演示 或 Google Slides 打开',
            'ppt': '使用 Microsoft PowerPoint、WPS演示 或 LibreOffice Impress 打开',
            'pdf': '使用 Adobe Reader、浏览器 或 其他PDF阅读器打开',
            'dwg': '使用 AutoCAD、DWG Viewer 或 免费的CAD查看器打开',
            'dxf': '使用 AutoCAD、FreeCAD 或 其他CAD软件打开'
        };

        return instructions[fileExtension] || '使用相应的软件打开此文件';
    }

    // 关闭手动指导对话框
    function closeManualInstructions() {
        const overlay = document.getElementById('manual-open-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // 取消下载
    function cancelDownload() {
        closeDownloadProgress();
    }

    // 关闭下载进度对话框
    function closeDownloadProgress() {
        const overlay = document.getElementById('download-progress-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // 打开下载的文件
    function openDownloadedFile(fileName, fileExtension) {
        autoOpenFile(fileName, fileExtension);
    }



    // 显示下载完成对话框
    function showDownloadCompleteDialog(fileName, fileExtension) {
        showAlert(`文件 "${fileName}" 下载完成！`, 'success');

        // 显示打开建议
        const openSuggestion = getOpenSuggestion(fileExtension);
        if (openSuggestion) {
            setTimeout(() => {
                showAlert(openSuggestion, 'info');
            }, 1500);
        }

        // 尝试提供更详细的操作指导
        setTimeout(() => {
            const detailedInstructions = getDetailedInstructions(fileExtension);
            if (detailedInstructions) {
                showAlert(detailedInstructions, 'info');
            }
        }, 3000);
    }

    // 获取详细的操作指导
    function getDetailedInstructions(fileExtension) {
        const instructions = {
            'dwg': '💡 提示：如果没有安装AutoCAD，可以使用免费的DWG Viewer或在线CAD查看器',
            'dxf': '💡 提示：DXF文件可以用多种CAD软件打开，包括免费的FreeCAD',
            'doc': '💡 提示：如果没有安装Word，可以使用WPS Office或在线Office查看',
            'docx': '💡 提示：可以使用Microsoft Word、WPS Office或Google Docs打开',
            'xls': '💡 提示：可以使用Microsoft Excel、WPS表格或Google Sheets打开',
            'xlsx': '💡 提示：Excel文件也可以用LibreOffice Calc等免费软件打开',
            'ppt': '💡 提示：可以使用PowerPoint、WPS演示或Google Slides打开',
            'pptx': '💡 提示：演示文稿文件支持多种软件打开，包括免费选项'
        };

        return instructions[fileExtension] || '💡 提示：文件已下载到默认下载文件夹，请使用相应的软件打开';
    }

    // 获取文件打开建议
    function getOpenSuggestion(fileExtension) {
        const suggestions = {
            'dwg': '建议使用 AutoCAD 或 DWG Viewer 打开 DWG 文件',
            'dxf': '建议使用 AutoCAD 或 DXF Viewer 打开 DXF 文件',
            'doc': '建议使用 Microsoft Word 打开 DOC 文件',
            'docx': '建议使用 Microsoft Word 打开 DOCX 文件',
            'xls': '建议使用 Microsoft Excel 打开 XLS 文件',
            'xlsx': '建议使用 Microsoft Excel 打开 XLSX 文件',
            'ppt': '建议使用 Microsoft PowerPoint 打开 PPT 文件',
            'pptx': '建议使用 Microsoft PowerPoint 打开 PPTX 文件',
            'step': '建议使用 CAD 软件打开 STEP 文件',
            'stp': '建议使用 CAD 软件打开 STP 文件',
            'iges': '建议使用 CAD 软件打开 IGES 文件',
            'igs': '建议使用 CAD 软件打开 IGS 文件'
        };

        return suggestions[fileExtension] || null;
    }

    // 删除附件
    function deleteAttachment(attachmentId) {
        if (!confirm('确定要删除这个附件吗？')) {
            return;
        }

        fetch(`/material_management/api/material/{{ material.id }}/attachments/${attachmentId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('附件删除成功！', 'success');
                loadAttachments(); // 重新加载附件列表
            } else {
                showAlert('删除失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showAlert('删除失败: ' + error, 'error');
        });
    }
</script>
{% endblock %}
