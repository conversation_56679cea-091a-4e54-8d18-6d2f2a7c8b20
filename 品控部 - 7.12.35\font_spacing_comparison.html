<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体大小和间距一致性对比 - 品质中心管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1976d2;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 15px;
        }
        .side-by-side {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .mockup {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .mockup-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2d3748;
            text-align: center;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 4px;
        }
        
        /* 模拟抽样检验记录的样式 */
        .sampling-mockup .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .sampling-mockup .header-left h1 {
            font-size: 18px;
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        .sampling-mockup .header-right {
            display: flex;
            gap: 8px;
        }
        .sampling-mockup .quick-search-form {
            display: flex;
            align-items: center;
            margin-right: 10px;
            position: relative;
            border: 1px solid #1976d2;
            border-radius: 4px;
            background-color: #fff;
            height: 32px;
            width: 180px;
        }
        .sampling-mockup .quick-search-input {
            height: 32px;
            width: 180px;
            padding: 2px 8px;
            padding-right: 30px;
            border: none;
            font-size: 12px;
            outline: none;
            background: transparent;
        }
        .sampling-mockup .btn {
            padding: 2px 5px;
            font-size: 11px;
            border: 1px solid;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
        }
        .sampling-mockup .sortable-table {
            font-size: 11px;
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .sampling-mockup .sortable-table th, .sampling-mockup .sortable-table td {
            padding: 3px 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
            font-size: 11px;
        }
        .sampling-mockup .sortable-table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        /* 模拟待检清单的样式（现在应该一致） */
        .pending-mockup .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .pending-mockup .header-left h1 {
            font-size: 18px;
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        .pending-mockup .header-right {
            display: flex;
            gap: 8px;
        }
        .pending-mockup .quick-search-form {
            display: flex;
            align-items: center;
            margin-right: 10px;
            position: relative;
            border: 1px solid #1976d2;
            border-radius: 4px;
            background-color: #fff;
            height: 32px;
            width: 180px;
        }
        .pending-mockup .quick-search-input {
            height: 32px;
            width: 180px;
            padding: 2px 8px;
            padding-right: 30px;
            border: none;
            font-size: 12px;
            outline: none;
            background: transparent;
        }
        .pending-mockup .btn {
            padding: 2px 5px;
            font-size: 11px;
            border: 1px solid;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
        }
        .pending-mockup .sortable-table {
            font-size: 11px;
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .pending-mockup .sortable-table th, .pending-mockup .sortable-table td {
            padding: 3px 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
            font-size: 11px;
        }
        .pending-mockup .sortable-table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        .btn-primary {
            background: #1976d2;
            color: white;
            border-color: #1976d2;
        }
        
        .highlight-box {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .specs-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        .specs-table th,
        .specs-table td {
            border: 1px solid #e2e8f0;
            padding: 12px;
            text-align: left;
        }
        .specs-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .match {
            color: #28a745;
            font-weight: 600;
        }
        .different {
            color: #dc3545;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .side-by-side {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 字体大小和间距一致性对比</h1>
        <p>确保待检清单页面与抽样检验记录页面的字体大小和导航栏间距完全一致</p>

        <div class="comparison-section">
            <div class="section-title">📐 页面布局对比</div>
            <div class="side-by-side">
                <div class="mockup sampling-mockup">
                    <div class="mockup-title">抽样检验记录（参考标准）</div>
                    <div class="page-header">
                        <div class="header-left">
                            <h1>抽样检验记录</h1>
                        </div>
                        <div class="header-right">
                            <div class="quick-search-form">
                                <input type="text" class="quick-search-input" placeholder="搜索...">
                                <span style="position: absolute; right: 8px; color: #1976d2;">🔍</span>
                            </div>
                            <a href="#" class="btn btn-success">高级搜索</a>
                            <a href="#" class="btn btn-primary">新增记录</a>
                        </div>
                    </div>
                    <table class="sortable-table">
                        <thead>
                            <tr>
                                <th>料号</th>
                                <th>名称</th>
                                <th>供应商</th>
                                <th>检验日期</th>
                                <th>结果</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MT001</td>
                                <td>电阻器</td>
                                <td>ABC电子</td>
                                <td>2024-12-01</td>
                                <td>合格</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="mockup pending-mockup">
                    <div class="mockup-title">待检清单（已调整一致）</div>
                    <div class="page-header">
                        <div class="header-left">
                            <h1>待检清单 - 抽样检验</h1>
                        </div>
                        <div class="header-right">
                            <div class="quick-search-form">
                                <input type="text" class="quick-search-input" placeholder="搜索...">
                                <span style="position: absolute; right: 8px; color: #1976d2;">🔍</span>
                            </div>
                            <a href="#" class="btn btn-success">批量导入</a>
                            <a href="#" class="btn btn-primary">新增检验</a>
                        </div>
                    </div>
                    <table class="sortable-table">
                        <thead>
                            <tr>
                                <th>料号</th>
                                <th>名称</th>
                                <th>供应商</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MT001</td>
                                <td>电阻器</td>
                                <td>ABC电子</td>
                                <td>2024-12-01</td>
                                <td>开始检验</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h3>✅ 一致性确认</h3>
            <p>经过调整，待检清单页面现在与抽样检验记录页面在以下方面完全一致：</p>
            <ul>
                <li><strong>页面头部间距：</strong>margin-bottom: 10px（移除了多余的padding和border）</li>
                <li><strong>搜索框样式：</strong>相同的边框、尺寸和字体大小</li>
                <li><strong>按钮样式：</strong>padding: 2px 5px, font-size: 11px</li>
                <li><strong>表格字体：</strong>font-size: 11px</li>
                <li><strong>表格间距：</strong>padding: 3px 6px</li>
                <li><strong>响应式布局：</strong>相同的移动端适配规则</li>
            </ul>
        </div>

        <div class="comparison-section">
            <div class="section-title">📊 详细规格对比</div>
            <table class="specs-table">
                <thead>
                    <tr>
                        <th>样式属性</th>
                        <th>抽样检验记录</th>
                        <th>待检清单（调整后）</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>页面标题字体</td>
                        <td>font-size: 18px</td>
                        <td>font-size: 18px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>页面头部间距</td>
                        <td>margin-bottom: 10px</td>
                        <td>margin-bottom: 10px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>搜索框字体</td>
                        <td>font-size: 12px</td>
                        <td>font-size: 12px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>搜索框尺寸</td>
                        <td>height: 32px, width: 180px</td>
                        <td>height: 32px, width: 180px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>按钮字体</td>
                        <td>font-size: 11px</td>
                        <td>font-size: 11px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>按钮内边距</td>
                        <td>padding: 2px 5px</td>
                        <td>padding: 2px 5px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>表格字体</td>
                        <td>font-size: 11px</td>
                        <td>font-size: 11px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>表格单元格间距</td>
                        <td>padding: 3px 6px</td>
                        <td>padding: 3px 6px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>表格背景色</td>
                        <td>background-color: #f5f5f5</td>
                        <td>background-color: #f5f5f5</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                    <tr>
                        <td>分页字体</td>
                        <td>font-size: 11px</td>
                        <td>font-size: 11px</td>
                        <td class="match">✅ 一致</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="comparison-section">
            <div class="section-title">🎯 调整总结</div>
            <h4>主要调整项目：</h4>
            <ul>
                <li><strong>页面头部：</strong>移除了多余的padding-bottom和border-bottom，保持与抽样检验记录一致的简洁样式</li>
                <li><strong>搜索框：</strong>采用完全相同的边框样式、尺寸和字体设置</li>
                <li><strong>按钮：</strong>调整为相同的小尺寸按钮样式（11px字体，2px 5px内边距）</li>
                <li><strong>表格：</strong>统一字体大小为11px，单元格间距为3px 6px</li>
                <li><strong>表格容器：</strong>移除多余的margin-top，保持简洁</li>
                <li><strong>响应式：</strong>采用相同的移动端适配规则</li>
            </ul>
            
            <h4>视觉效果：</h4>
            <p>现在两个页面具有完全一致的视觉风格和用户体验，用户在不同页面间切换时不会感到任何不协调。</p>
        </div>
    </div>
</body>
</html>
