// QMS文件管理器 - 弹出页面脚本

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const localAppStatus = document.getElementById('local-app-status');
    const qmsServerStatus = document.getElementById('qms-server-status');
    const downloadCount = document.getElementById('download-count');
    const downloadPath = document.getElementById('download-path');
    const localServerUrl = document.getElementById('local-server-url');
    const autoDownload = document.getElementById('auto-download');
    const saveSettingsBtn = document.getElementById('save-settings');
    const testConnectionBtn = document.getElementById('test-connection');
    const openLocalAppBtn = document.getElementById('open-local-app');

    // 初始化
    loadSettings();
    checkStatus();

    // 事件监听器
    saveSettingsBtn.addEventListener('click', saveSettings);
    testConnectionBtn.addEventListener('click', testConnection);
    openLocalAppBtn.addEventListener('click', openLocalApp);

    // 加载设置
    function loadSettings() {
        chrome.storage.local.get([
            'downloadPath',
            'localServerUrl',
            'autoDownload',
            'downloadCount'
        ], function(result) {
            downloadPath.value = result.downloadPath || 'C:\\QMS1\\';
            localServerUrl.value = result.localServerUrl || 'http://localhost:8765';
            autoDownload.checked = result.autoDownload !== false;
            downloadCount.textContent = result.downloadCount || 0;
        });
    }

    // 保存设置
    function saveSettings() {
        const settings = {
            downloadPath: downloadPath.value.trim(),
            localServerUrl: localServerUrl.value.trim(),
            autoDownload: autoDownload.checked
        };

        // 验证设置
        if (!settings.downloadPath) {
            showMessage('请输入下载路径', 'error');
            return;
        }

        if (!settings.localServerUrl) {
            showMessage('请输入本地应用地址', 'error');
            return;
        }

        // 保存到存储
        chrome.storage.local.set(settings, function() {
            showMessage('设置已保存', 'success');
            
            // 通知后台脚本设置已更新
            chrome.runtime.sendMessage({
                action: 'saveSettings',
                settings: settings
            });
        });
    }

    // 检查状态
    function checkStatus() {
        // 检查本地应用连接
        checkLocalAppConnection();
        
        // 检查QMS服务器连接
        checkQMSServerConnection();
        
        // 获取下载统计
        updateDownloadStats();
    }

    // 检查本地应用连接
    function checkLocalAppConnection() {
        chrome.runtime.sendMessage({
            action: 'checkLocalApp'
        }, function(response) {
            if (response && response.success) {
                if (response.connected) {
                    updateStatus(localAppStatus, '已连接', 'connected');
                } else {
                    updateStatus(localAppStatus, '未连接', 'disconnected');
                }
            } else {
                updateStatus(localAppStatus, '检查失败', 'error');
            }
        });
    }

    // 检查QMS服务器连接
    function checkQMSServerConnection() {
        // 尝试访问QMS服务器
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            const currentTab = tabs[0];
            if (currentTab && currentTab.url && currentTab.url.includes('192.168.50.203:5000')) {
                updateStatus(qmsServerStatus, '已连接', 'connected');
            } else {
                updateStatus(qmsServerStatus, '未在QMS页面', 'disconnected');
            }
        });
    }

    // 更新下载统计
    function updateDownloadStats() {
        chrome.storage.local.get(['downloadCount'], function(result) {
            downloadCount.textContent = result.downloadCount || 0;
        });
    }

    // 更新状态显示
    function updateStatus(element, text, status) {
        element.textContent = text;
        element.className = `status-value status-${status}`;
    }

    // 测试连接
    function testConnection() {
        testConnectionBtn.disabled = true;
        testConnectionBtn.textContent = '测试中...';

        // 测试本地应用连接
        const serverUrl = localServerUrl.value.trim();
        
        fetch(`${serverUrl}/ping`, {
            method: 'GET',
            timeout: 5000
        })
        .then(response => {
            if (response.ok) {
                showMessage('本地应用连接正常', 'success');
                updateStatus(localAppStatus, '已连接', 'connected');
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        })
        .catch(error => {
            showMessage(`连接失败: ${error.message}`, 'error');
            updateStatus(localAppStatus, '连接失败', 'error');
        })
        .finally(() => {
            testConnectionBtn.disabled = false;
            testConnectionBtn.textContent = '测试连接';
        });
    }

    // 打开本地应用
    function openLocalApp() {
        // 尝试通过协议打开本地应用
        const protocolUrl = 'qms-file-manager://open';
        
        // 创建一个隐藏的链接来触发协议
        const link = document.createElement('a');
        link.href = protocolUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 显示提示信息
        showMessage('正在尝试打开本地应用...', 'info');
        
        // 如果协议不工作，显示手动启动提示
        setTimeout(() => {
            showMessage('如果应用未启动，请手动运行QMSFileManager.exe', 'info');
        }, 2000);
    }

    // 显示消息
    function showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessage = document.querySelector('.message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            right: 10px;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            animation: slideDown 0.3s ease-out;
        `;

        // 设置样式
        const styles = {
            success: { background: '#e8f5e8', color: '#2e7d32', border: '1px solid #4caf50' },
            error: { background: '#ffebee', color: '#c62828', border: '1px solid #f44336' },
            info: { background: '#e3f2fd', color: '#1565c0', border: '1px solid #2196f3' }
        };

        const style = styles[type] || styles.info;
        Object.assign(messageEl.style, style);
        messageEl.textContent = message;

        document.body.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.style.animation = 'slideUp 0.3s ease-in';
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.remove();
                    }
                }, 300);
            }
        }, 3000);
    }

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes slideUp {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(-100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // 定期更新状态
    setInterval(checkStatus, 10000); // 每10秒检查一次状态
});
