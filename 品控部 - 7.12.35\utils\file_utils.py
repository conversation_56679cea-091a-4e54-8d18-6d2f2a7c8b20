"""
文件和目录管理工具函数
"""
import os
import logging
from flask import current_app

def ensure_directory_exists(directory_path):
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path (str): 目录路径
        
    Returns:
        bool: 成功返回True，失败返回False
    """
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path, exist_ok=True)
            logging.info(f"创建目录: {directory_path}")
        return True
    except Exception as e:
        logging.error(f"无法创建目录 {directory_path}: {str(e)}")
        return False

def check_directory_writable(directory_path):
    """
    检查目录是否可写
    
    Args:
        directory_path (str): 目录路径
        
    Returns:
        bool: 可写返回True，不可写返回False
    """
    try:
        test_file = os.path.join(directory_path, '.write_test')
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        return True
    except Exception as e:
        logging.error(f"目录不可写 {directory_path}: {str(e)}")
        return False

def get_safe_upload_path(base_path, sub_dirs=None):
    """
    获取安全的上传路径
    
    Args:
        base_path (str): 基础路径
        sub_dirs (list): 子目录列表
        
    Returns:
        str: 安全的上传路径
    """
    try:
        # 构建完整路径
        if sub_dirs:
            full_path = os.path.join(base_path, *sub_dirs)
        else:
            full_path = base_path
            
        # 确保目录存在
        if ensure_directory_exists(full_path):
            return full_path
        else:
            # 如果无法创建指定目录，返回基础路径
            if ensure_directory_exists(base_path):
                return base_path
            else:
                # 最后的备选方案：使用应用的上传目录
                fallback_path = current_app.config.get('UPLOAD_FOLDER', 'static/uploads')
                ensure_directory_exists(fallback_path)
                return fallback_path
                
    except Exception as e:
        logging.error(f"获取上传路径失败: {str(e)}")
        # 返回默认路径
        fallback_path = current_app.config.get('UPLOAD_FOLDER', 'static/uploads')
        ensure_directory_exists(fallback_path)
        return fallback_path

def validate_file_size(file, max_size_mb=5):
    """
    验证文件大小
    
    Args:
        file: 文件对象
        max_size_mb (int): 最大大小（MB）
        
    Returns:
        bool: 符合大小限制返回True，否则返回False
    """
    try:
        # 获取文件大小
        file.seek(0, 2)  # 移动到文件末尾
        size = file.tell()
        file.seek(0)  # 重置文件指针
        
        max_size_bytes = max_size_mb * 1024 * 1024
        return size <= max_size_bytes
    except Exception as e:
        logging.error(f"验证文件大小失败: {str(e)}")
        return False

def get_system_setting(key, default_value=None):
    """
    获取系统设置值
    
    Args:
        key (str): 设置键
        default_value: 默认值
        
    Returns:
        设置值或默认值
    """
    try:
        from db_config import get_db_connection
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("SELECT setting_value FROM system_settings WHERE setting_key = %s", (key,))
        result = cursor.fetchone()
        
        if result and result['setting_value']:
            return result['setting_value']
        else:
            return default_value
            
    except Exception as e:
        logging.error(f"获取系统设置失败 {key}: {str(e)}")
        return default_value
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
