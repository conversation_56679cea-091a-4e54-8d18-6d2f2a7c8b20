# 数据库管理文件说明

## 📁 文件结构

```
database/
├── create_tables.py        # 数据表创建脚本
├── generate_sample_data.py # 示例数据生成脚本
└── README.md              # 说明文档

根目录/
├── db_config.py           # 数据库配置和连接管理
└── config.py              # 应用配置文件
```

## 🎯 文件功能说明

### 1. `create_tables.py` - 数据表创建脚本
**功能**: 创建品质中心管理系统的所有数据表和索引

**包含的数据表**:
- `sampling_inspection` - 抽样检验记录表
- `materials` - 物料信息表
- `workshop_abnormal` - 车间异常记录表
- `system_settings` - 系统设置表

**使用方法**:
```bash
# 创建所有数据表和索引
python database/create_tables.py
```

**功能特性**:
- ✅ 自动创建数据库（如果不存在）
- ✅ 创建所有必要的数据表
- ✅ 创建优化查询的索引
- ✅ 插入默认系统设置
- ✅ 详细的执行日志

### 2. `generate_sample_data.py` - 示例数据生成脚本
**功能**: 生成用于测试和演示的示例数据

**生成的数据**:
- 15种物料基础数据
- 150条抽样检验记录
- 真实的问题点描述

**使用方法**:
```bash
# 生成示例数据
python database/generate_sample_data.py
```

**数据特性**:
- ✅ 时间分布在最近6个月
- ✅ 包含5大类质量问题
- ✅ 真实的供应商和检验员信息
- ✅ 支持历史问题点查询测试

### 3. `db_config.py` - 数据库配置（根目录）
**功能**: 提供数据库连接和基本操作功能

**主要函数**:
- `get_db_connection()` - 获取数据库连接
- `get_mysql_connection()` - 获取MySQL服务器连接
- `test_connection()` - 测试数据库连接
- `get_table_info()` - 获取数据库表信息

**使用方法**:
```bash
# 测试数据库连接
python db_config.py
```

### 4. `config.py` - 应用配置（根目录）
**功能**: 管理应用的所有配置信息

**配置内容**:
- 数据库连接参数
- 文件上传设置
- 会话管理配置
- 环境变量支持

## 🚀 快速开始

### 1. 初始化数据库
```bash
# 第一步：创建数据表
python database/create_tables.py

# 第二步：生成示例数据
python database/generate_sample_data.py
```

### 2. 验证安装
```bash
# 测试数据库连接
python db_config.py
```

### 3. 启动应用
```bash
# 启动Web应用
python app.py
```

## 🔧 配置说明

### 数据库配置
在 `config.py` 中配置数据库连接参数：

```python
class Config:
    DB_HOST = 'localhost'
    DB_USER = 'root'
    DB_PASSWORD = 'your_password'
    DB_NAME = 'quality_control'
```

### 环境变量支持
可以通过环境变量覆盖默认配置：

```bash
export DB_HOST=localhost
export DB_USER=root
export DB_PASSWORD=your_password
export DB_NAME=quality_control
```

## 📊 数据表结构

### 抽样检验表 (sampling_inspection)
- 物料信息：料号、名称、规格、材质、颜色
- 检验信息：供应商、采购单号、收货日期、检验日期
- 数量信息：总数量、抽样数量、合格数量、缺陷数量
- 质量信息：缺陷问题详情、检验员

### 物料表 (materials)
- 基本信息：料号、名称、规格、材质、颜色
- 扩展信息：备注说明
- 时间信息：创建时间、更新时间

### 车间异常记录表 (workshop_abnormal)
- 产品信息：产品编号、产品名称
- 异常信息：车间、工序、异常类型、异常详情
- 记录信息：记录日期、记录人

### 系统设置表 (system_settings)
- 设置项：设置键、设置值、描述
- 时间信息：创建时间、更新时间

## 🔍 索引优化

为提高查询性能，创建了以下索引：

### 抽样检验表索引
- `idx_sampling_material_number` - 物料编号索引
- `idx_sampling_supplier` - 供应商索引
- `idx_sampling_dates` - 日期复合索引
- `idx_sampling_search` - 搜索复合索引

### 全部检验表索引
- `idx_full_material_number` - 物料编号索引
- `idx_full_supplier` - 供应商索引
- `idx_full_dates` - 日期复合索引
- `idx_full_search` - 搜索复合索引

### 车间异常记录表索引
- `idx_workshop_abnormal_product` - 产品编号索引
- `idx_workshop_abnormal_workshop` - 车间索引
- `idx_workshop_abnormal_date` - 日期索引
- `idx_workshop_abnormal_type` - 异常类型索引

## 🛠️ 维护和故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证连接参数是否正确
   - 确认用户权限是否足够

2. **表创建失败**
   - 检查数据库是否存在
   - 验证用户是否有CREATE权限
   - 查看错误日志获取详细信息

3. **数据导入失败**
   - 确认表结构是否正确
   - 检查数据格式是否符合要求
   - 验证外键约束是否满足

### 重置数据库
如需重置数据库，可以执行以下步骤：

```bash
# 1. 删除所有表（谨慎操作）
# 在MySQL中执行：DROP DATABASE quality_control;

# 2. 重新创建数据库和表
python database/create_tables.py

# 3. 重新生成示例数据
python database/generate_sample_data.py
```

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 重构数据库文件结构
- ✅ 分离表创建和数据生成功能
- ✅ 移动配置文件到根目录
- ✅ 删除无用和重复文件
- ✅ 优化索引设计
- ✅ 完善文档说明

## 🎯 下一步计划

- [ ] 添加数据备份和恢复功能
- [ ] 实现数据库版本迁移机制
- [ ] 添加性能监控和优化工具
- [ ] 支持多数据库类型（PostgreSQL、SQLite）
