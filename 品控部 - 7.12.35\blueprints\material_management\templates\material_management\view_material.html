{% extends "base.html" %}

{% block title %}物料详情 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    .material-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .detail-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .card-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .info-item {
        display: flex;
        flex-direction: column;
    }
    
    .info-label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
        margin-bottom: 5px;
        text-transform: uppercase;
    }
    
    .info-value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
    }
    
    .info-value.empty {
        color: #999;
        font-style: italic;
    }
    
    .material-number {
        font-size: 16px;
        font-weight: 600;
        color: #1976d2;
    }
    
    .material-type-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
    }
    
    .type-metal {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .type-plastic {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    
    .type-rubber {
        background: #e8f5e8;
        color: #388e3c;
    }
    
    .type-glass {
        background: #fff3e0;
        color: #f57c00;
    }
    
    .type-other {
        background: #f5f5f5;
        color: #666;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        color: white;
        padding: 15px;
        border-radius: 6px;
        text-align: center;
    }
    
    .stats-card.sampling {
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    }
    
    .stats-card.full {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    }
    
    .stats-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-label {
        font-size: 12px;
        opacity: 0.9;
    }
    
    .btn-group {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 13px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }
    
    .btn-primary {
        background: #1976d2;
        color: white;
    }
    
    .btn-primary:hover {
        background: #1565c0;
    }
    
    .btn-warning {
        background: #ff9800;
        color: white;
    }
    
    .btn-warning:hover {
        background: #f57c00;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
    }
    
    .description-box {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin-top: 15px;
        font-size: 13px;
        line-height: 1.5;
        color: #495057;
    }
    
    .description-box.empty {
        color: #999;
        font-style: italic;
        text-align: center;
    }
    
    @media (max-width: 768px) {
        .material-detail-container {
            padding: 10px;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .btn-group {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>物料详情</h1>
</div>

<div class="material-detail-container">
    <!-- 基本信息卡片 -->
    <div class="detail-card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-box"></i>
                基本信息
            </h2>
            <div class="btn-group">
                <a href="{{ url_for('material_management.edit_material', material_id=material.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> 编辑
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">物料料号</div>
                    <div class="info-value material-number">{{ material.material_number }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">物料名称</div>
                    <div class="info-value">{{ material.material_name }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">规格</div>
                    <div class="info-value {% if not material.specification %}empty{% endif %}">
                        {{ material.specification or '未填写' }}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">材质</div>
                    <div class="info-value">
                        {% if material.material_type %}
                            <span class="material-type-badge {% if '钢' in material.material_type or '铝' in material.material_type or '铜' in material.material_type or '钛' in material.material_type or '镁' in material.material_type %}type-metal{% elif '塑料' in material.material_type or 'ABS' in material.material_type or '尼龙' in material.material_type %}type-plastic{% elif '橡胶' in material.material_type or '硅' in material.material_type %}type-rubber{% elif '玻璃' in material.material_type or '陶瓷' in material.material_type %}type-glass{% else %}type-other{% endif %}">
                                {{ material.material_type }}
                            </span>
                        {% else %}
                            <span class="info-value empty">未填写</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">颜色</div>
                    <div class="info-value {% if not material.color %}empty{% endif %}">
                        {{ material.color or '未填写' }}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">创建时间</div>
                    <div class="info-value">
                        {{ material.created_at.strftime('%Y-%m-%d %H:%M:%S') if material.created_at else '未知' }}
                    </div>
                </div>
            </div>
            
            {% if material.description %}
                <div class="info-item">
                    <div class="info-label">备注说明</div>
                    <div class="description-box">{{ material.description }}</div>
                </div>
            {% else %}
                <div class="info-item">
                    <div class="info-label">备注说明</div>
                    <div class="description-box empty">暂无备注说明</div>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- 检验统计卡片 -->
    <div class="detail-card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-chart-bar"></i>
                检验统计
            </h2>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stats-card sampling">
                    <div class="stats-number">{{ sampling_stats.sampling_count or 0 }}</div>
                    <div class="stats-label">抽样检验次数</div>
                </div>
                
                <div class="stats-card full">
                    <div class="stats-number">{{ full_stats.full_count or 0 }}</div>
                    <div class="stats-label">全部检验次数</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-number">{{ (sampling_stats.total_sampling_qty or 0) + (full_stats.total_full_qty or 0) }}</div>
                    <div class="stats-label">总检验数量</div>
                </div>
                
                <div class="stats-card" style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);">
                    <div class="stats-number">{{ (sampling_stats.total_sampling_defects or 0) + (full_stats.total_full_defects or 0) }}</div>
                    <div class="stats-label">总缺陷数量</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="btn-group">
        <a href="{{ url_for('material_management.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
        <a href="{{ url_for('material_management.edit_material', material_id=material.id) }}" class="btn btn-warning">
            <i class="fas fa-edit"></i> 编辑物料
        </a>
        <a href="#" class="btn btn-primary" onclick="viewInspectionHistory()">
            <i class="fas fa-history"></i> 查看检验历史
        </a>
    </div>
</div>

<script>
    function viewInspectionHistory() {
        // 这里可以跳转到检验历史页面或打开弹窗
        alert('检验历史功能开发中...');
        // 示例：跳转到来料检验页面并自动填入物料料号
        // window.location.href = `/incoming_inspection/sampling?material_number={{ material.material_number }}`;
    }
</script>
{% endblock %}
