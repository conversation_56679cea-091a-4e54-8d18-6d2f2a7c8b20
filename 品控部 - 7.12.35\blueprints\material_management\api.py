from flask import request, jsonify, send_file, current_app, Response
from . import material_management_bp
from db_config import get_db_connection
import json
import os
import uuid
import mimetypes
import shutil
import platform
import re
from datetime import datetime
from werkzeug.utils import secure_filename
from urllib.parse import quote

def encode_filename_for_http(filename):
    """
    为HTTP头编码文件名，支持中文字符
    """
    try:
        # 尝试ASCII编码
        filename.encode('ascii')
        return f'filename="{filename}"'
    except UnicodeEncodeError:
        # 如果包含非ASCII字符，使用RFC 5987编码
        encoded_filename = quote(filename.encode('utf-8'))
        return f'filename*=UTF-8\'\'{encoded_filename}'

def safe_filename(filename):
    """
    安全的文件名处理，支持中文字符
    确保返回的文件名既安全又保留原始可读性
    """
    if not filename:
        return "unnamed_file"

    # 保存原始文件名用于备用
    original_filename = filename.strip()

    # 首先尝试保留原始文件名，只替换危险字符
    # 保留中文、日文、韩文、数字、字母、常见符号
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', original_filename)
    safe_name = re.sub(r'[^\w\s\-_\.\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff]', '_', safe_name)

    # 清理多余的下划线和空格
    safe_name = re.sub(r'[_\s]+', '_', safe_name).strip('_')

    # 确保文件名有意义且不为空
    if not safe_name or safe_name == '.' or len(safe_name.replace('.', '').replace('_', '')) < 1:
        # 如果处理后为空，使用时间戳作为文件名，但保留原始扩展名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if '.' in original_filename:
            ext = original_filename.rsplit('.', 1)[1]
            return f"file_{timestamp}.{ext}"
        else:
            return f"file_{timestamp}"

    # 如果文件名太短，尝试使用secure_filename作为备选
    if len(safe_name) < 3:
        secure_name = secure_filename(original_filename)
        if secure_name and len(secure_name) >= 3:
            return secure_name
        else:
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if '.' in original_filename:
                ext = original_filename.rsplit('.', 1)[1]
                return f"file_{timestamp}.{ext}"
            else:
                return f"file_{timestamp}"

    return safe_name

@material_management_bp.route('/api/materials', methods=['GET'])
def get_materials():
    """获取物料列表"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        search = request.args.get('search', '').strip()
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 构建查询条件
        where_clause = ""
        params = []
        
        if search:
            where_clause = """
                WHERE material_number LIKE %s 
                OR material_name LIKE %s 
                OR specification LIKE %s
                OR material_type LIKE %s
            """
            search_param = f"%{search}%"
            params = [search_param, search_param, search_param, search_param]
        
        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM materials {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 获取分页数据
        offset = (page - 1) * per_page
        query = f"""
            SELECT * FROM materials 
            {where_clause}
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
        """
        cursor.execute(query, params + [per_page, offset])
        materials = cursor.fetchall()
        
        return jsonify({
            "success": True,
            "materials": materials,
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material', methods=['POST'])
def add_material():
    """新增物料"""
    try:
        data = request.get_json()
        
        material_number = data.get('material_number', '').strip()
        material_name = data.get('material_name', '').strip()
        specification = data.get('specification', '').strip()
        material_type = data.get('material_type', '').strip()
        color = data.get('color', '').strip()
        material_category = data.get('material_category', '').strip()
        inspection_type = data.get('inspection_type', '').strip()
        description = data.get('description', '').strip()

        if not material_number or not material_name:
            return jsonify({"success": False, "error": "物料料号和名称不能为空"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查料号是否已存在
        cursor.execute("SELECT id FROM materials WHERE material_number = %s", (material_number,))
        if cursor.fetchone():
            return jsonify({"success": False, "error": "物料料号已存在"}), 400

        # 插入新物料
        cursor.execute("""
            INSERT INTO materials (material_number, material_name, specification, material_type, color, material_category, inspection_type, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (material_number, material_name, specification, material_type, color, material_category, inspection_type, description))
        
        conn.commit()
        
        return jsonify({"success": True, "message": "物料添加成功"})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/<material_id>', methods=['PUT'])
def update_material(material_id):
    """更新物料"""
    try:
        data = request.get_json()
        
        material_number = data.get('material_number', '').strip()
        material_name = data.get('material_name', '').strip()
        specification = data.get('specification', '').strip()
        material_type = data.get('material_type', '').strip()
        color = data.get('color', '').strip()
        material_category = data.get('material_category', '').strip()
        inspection_type = data.get('inspection_type', '').strip()
        description = data.get('description', '').strip()

        if not material_number or not material_name:
            return jsonify({"success": False, "error": "物料料号和名称不能为空"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查物料是否存在
        cursor.execute("SELECT material_number FROM materials WHERE id = %s", (material_id,))
        current_material = cursor.fetchone()
        if not current_material:
            return jsonify({"success": False, "error": "物料不存在"}), 404

        # 如果料号发生变化，检查新料号是否已存在
        if current_material[0] != material_number:
            cursor.execute("SELECT id FROM materials WHERE material_number = %s AND id != %s",
                         (material_number, material_id))
            if cursor.fetchone():
                return jsonify({"success": False, "error": "物料料号已存在"}), 400

        # 更新物料
        cursor.execute("""
            UPDATE materials
            SET material_number = %s, material_name = %s, specification = %s,
                material_type = %s, color = %s, material_category = %s, inspection_type = %s, description = %s,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (material_number, material_name, specification, material_type, color, material_category, inspection_type, description, material_id))
        
        conn.commit()
        
        return jsonify({"success": True, "message": "物料更新成功"})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/<material_id>', methods=['DELETE'])
def delete_material(material_id):
    """删除物料"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查是否有相关检验记录
        cursor.execute("""
            SELECT material_number FROM materials WHERE id = %s
        """, (material_id,))
        material = cursor.fetchone()
        
        if not material:
            return jsonify({"success": False, "error": "物料不存在"}), 404
        
        material_number = material[0]
        
        # 检查是否有检验记录
        cursor.execute("""
            SELECT COUNT(*) as count FROM sampling_inspection WHERE material_number = %s
            UNION ALL
            SELECT COUNT(*) as count FROM full_inspection WHERE material_number = %s
        """, (material_number, material_number))
        
        inspection_counts = cursor.fetchall()
        total_inspections = sum(row[0] for row in inspection_counts)
        
        if total_inspections > 0:
            return jsonify({
                "success": False, 
                "error": f"该物料存在 {total_inspections} 条检验记录，无法删除"
            }), 400
        
        # 删除物料
        cursor.execute("DELETE FROM materials WHERE id = %s", (material_id,))
        conn.commit()
        
        return jsonify({"success": True, "message": "物料删除成功"})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/<int:material_id>', methods=['PATCH'])
def update_material_field(material_id):
    """部分更新物料字段"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"success": False, "error": "没有提供更新数据"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查物料是否存在
        cursor.execute("SELECT id FROM materials WHERE id = %s", (material_id,))
        if not cursor.fetchone():
            return jsonify({"success": False, "error": "物料不存在"}), 404

        # 构建更新SQL
        update_fields = []
        update_values = []

        # 允许更新的字段
        allowed_fields = ['material_category', 'inspection_type', 'material_name', 'specification', 'material_type', 'color', 'description']

        for field, value in data.items():
            if field in allowed_fields:
                update_fields.append(f"{field} = %s")
                update_values.append(value)

        if not update_fields:
            return jsonify({"success": False, "error": "没有有效的更新字段"}), 400

        # 添加更新时间
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        update_values.append(material_id)

        # 执行更新
        sql = f"UPDATE materials SET {', '.join(update_fields)} WHERE id = %s"
        cursor.execute(sql, update_values)

        conn.commit()

        return jsonify({"success": True, "message": "物料更新成功"})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 附件管理API

@material_management_bp.route('/api/material/<int:material_id>/attachments', methods=['POST'])
def upload_attachment(material_id):
    """上传物料附件"""
    try:
        # 检查物料是否存在
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id FROM materials WHERE id = %s", (material_id,))
        if not cursor.fetchone():
            return jsonify({"success": False, "error": "物料不存在"}), 404

        # 检查文件
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "没有选择文件"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "没有选择文件"}), 400

        # 获取附件描述
        description = request.form.get('description', '').strip()

        # 验证文件类型和大小
        allowed_extensions = {
            'jpg', 'jpeg', 'png', 'gif', 'bmp',  # 图片
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',  # 文档
            'dwg', 'dxf', 'step', 'stp', 'iges', 'igs',  # 图纸
            'txt', 'csv', 'xml', 'json'  # 其他
        }

        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        if file_extension not in allowed_extensions:
            return jsonify({"success": False, "error": "不支持的文件类型"}), 400

        # 检查文件大小 (10MB)
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        if file_size > 10 * 1024 * 1024:  # 10MB
            return jsonify({"success": False, "error": "文件大小不能超过10MB"}), 400

        # 生成安全的文件名 - 保存原始文件名用于数据库存储
        safe_original_filename = safe_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{safe_original_filename}"

        # 根据文件类型确定存储目录
        if file_extension in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
            subfolder = 'images'
        elif file_extension in ['dwg', 'dxf', 'step', 'stp', 'iges', 'igs']:
            subfolder = 'drawings'
        elif file_extension in ['pdf']:
            subfolder = 'documents'
        else:
            subfolder = 'documents'

        # 创建上传目录 - 使用绝对路径
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        upload_dir = os.path.join(base_dir, 'static', 'uploads', 'material_attachments', subfolder)
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 数据库中保存相对路径
        relative_path = os.path.join('static', 'uploads', 'material_attachments', subfolder, unique_filename)

        # 保存到数据库
        cursor.execute("""
            INSERT INTO material_attachments
            (material_id, file_name, file_path, file_size, file_type, file_extension, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (material_id, safe_original_filename, relative_path, file_size, file.content_type or 'application/octet-stream', file_extension, description))

        conn.commit()

        return jsonify({"success": True, "message": "文件上传成功"})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/<int:material_id>/attachments', methods=['GET'])
def get_attachments(material_id):
    """获取物料附件列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 检查物料是否存在
        cursor.execute("SELECT id FROM materials WHERE id = %s", (material_id,))
        if not cursor.fetchone():
            return jsonify({"success": False, "error": "物料不存在"}), 404

        # 获取附件列表
        cursor.execute("""
            SELECT id, file_name, file_size, file_type, file_extension,
                   upload_time, description, is_active
            FROM material_attachments
            WHERE material_id = %s AND is_active = TRUE
            ORDER BY upload_time DESC
        """, (material_id,))

        attachments = cursor.fetchall()

        return jsonify({
            "success": True,
            "attachments": attachments
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/<int:material_id>/attachments/<int:attachment_id>/download', methods=['GET'])
def download_attachment(material_id, attachment_id):
    """下载附件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 获取附件信息
        cursor.execute("""
            SELECT file_name, file_path
            FROM material_attachments
            WHERE id = %s AND material_id = %s AND is_active = TRUE
        """, (attachment_id, material_id))

        attachment = cursor.fetchone()
        if not attachment:
            return jsonify({"success": False, "error": "附件不存在"}), 404

        # 构建完整的文件路径
        if os.path.isabs(attachment['file_path']):
            full_file_path = attachment['file_path']
        else:
            # 如果是相对路径，构建绝对路径
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            full_file_path = os.path.join(base_dir, attachment['file_path'])

        # 检查文件是否存在
        if not os.path.exists(full_file_path):
            return jsonify({"success": False, "error": f"文件不存在: {attachment['file_path']}"}), 404

        # 根据文件类型决定是否在浏览器中直接打开
        file_extension = attachment['file_name'].split('.')[-1].lower() if '.' in attachment['file_name'] else ''

        # 可以在浏览器中直接打开的文件类型
        inline_types = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'txt', 'html', 'htm', 'xml', 'json']

        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(attachment['file_name'])
        if not mime_type:
            mime_type = 'application/octet-stream'

        if file_extension in inline_types:
            # 在浏览器中直接打开，添加缓存控制
            response = send_file(
                full_file_path,
                as_attachment=False,  # 不强制下载
                download_name=attachment['file_name'],
                mimetype=mime_type
            )

            # 添加缓存控制头，允许浏览器缓存文件
            response.headers['Cache-Control'] = 'public, max-age=3600'  # 缓存1小时
            response.headers['Content-Disposition'] = f'inline; {encode_filename_for_http(attachment["file_name"])}'

            return response
        else:
            # 其他文件类型强制下载，确保能被浏览器正确处理
            response = send_file(
                full_file_path,
                as_attachment=True,
                download_name=attachment['file_name'],
                mimetype=mime_type
            )

            # 添加下载相关的头信息
            response.headers['Content-Disposition'] = f'attachment; {encode_filename_for_http(attachment["file_name"])}'
            response.headers['Content-Description'] = 'File Transfer'
            response.headers['Content-Transfer-Encoding'] = 'binary'
            response.headers['Cache-Control'] = 'must-revalidate'
            response.headers['Pragma'] = 'public'

            return response

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/<int:material_id>/attachments/<int:attachment_id>', methods=['DELETE'])
def delete_attachment(material_id, attachment_id):
    """删除附件"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 获取附件信息
        cursor.execute("""
            SELECT file_path
            FROM material_attachments
            WHERE id = %s AND material_id = %s AND is_active = TRUE
        """, (attachment_id, material_id))

        attachment = cursor.fetchone()
        if not attachment:
            return jsonify({"success": False, "error": "附件不存在"}), 404

        # 软删除（标记为无效）
        cursor.execute("""
            UPDATE material_attachments
            SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s AND material_id = %s
        """, (attachment_id, material_id))

        conn.commit()

        # 可选：删除物理文件（这里选择保留，只做软删除）
        # try:
        #     if os.path.exists(attachment['file_path']):
        #         os.remove(attachment['file_path'])
        # except:
        #     pass  # 忽略文件删除错误

        return jsonify({"success": True, "message": "附件删除成功"})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/attachments/<material_number>', methods=['GET'])
def get_attachments_by_material_number(material_number):
    """通过物料料号获取附件列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 先通过料号获取物料ID
        cursor.execute("SELECT id FROM materials WHERE material_number = %s", (material_number,))
        material = cursor.fetchone()

        if not material:
            return jsonify({"success": False, "error": "物料不存在"})

        material_id = material['id']

        # 获取附件列表
        cursor.execute("""
            SELECT id, file_name, file_path, file_size, file_type, file_extension,
                   upload_time, description, is_active
            FROM material_attachments
            WHERE material_id = %s AND is_active = TRUE
            ORDER BY upload_time DESC
        """, (material_id,))

        attachments = cursor.fetchall()

        # 转换为前端需要的格式
        formatted_attachments = []
        for attachment in attachments:
            formatted_attachments.append({
                'id': attachment['id'],
                'filename': attachment['file_name'],
                'extension': attachment['file_extension'],
                'path': f"/material_management/api/material/{material_id}/attachments/{attachment['id']}/download",
                'size': attachment['file_size'],
                'upload_time': attachment['upload_time'].strftime('%Y-%m-%d %H:%M:%S') if attachment['upload_time'] else '',
                'description': attachment['description'] or ''
            })

        return jsonify({
            "success": True,
            "attachments": formatted_attachments
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/material/<int:material_id>/attachments/<int:attachment_id>/download_to_local', methods=['POST'])
def download_attachment_to_local(material_id, attachment_id):
    """下载附件到本地指定路径"""
    try:
        data = request.get_json()
        local_path = data.get('local_path', 'C:\\QMS1\\')

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 获取附件信息
        cursor.execute("""
            SELECT file_name, file_path, file_size
            FROM material_attachments
            WHERE id = %s AND material_id = %s AND is_active = TRUE
        """, (attachment_id, material_id))

        attachment = cursor.fetchone()

        if attachment:
            source_file_path = attachment['file_path']

            # 构建完整的源文件路径
            if os.path.isabs(source_file_path):
                full_source_path = source_file_path
            else:
                # 如果是相对路径，构建绝对路径
                base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                full_source_path = os.path.join(base_dir, source_file_path)

            # 标准化路径
            full_source_path = os.path.normpath(full_source_path)

            # 确保本地路径存在
            os.makedirs(local_path, exist_ok=True)

            # 目标文件路径
            target_file = os.path.join(local_path, attachment['file_name'])

            # 调试信息
            print(f"DEBUG: 源文件路径: {full_source_path}")
            print(f"DEBUG: 目标文件路径: {target_file}")
            print(f"DEBUG: 源文件存在: {os.path.exists(full_source_path)}")

            # 复制文件到本地路径
            if os.path.exists(full_source_path):
                shutil.copy2(full_source_path, target_file)

                # 验证文件是否复制成功
                if os.path.exists(target_file):
                    return jsonify({
                        "success": True,
                        "message": "文件下载成功",
                        "local_file_path": target_file,
                        "file_name": attachment['file_name'],
                        "file_size": attachment['file_size']
                    })
                else:
                    return jsonify({"success": False, "error": "文件复制失败"}), 500
            else:
                return jsonify({"success": False, "error": f"源文件不存在: {full_source_path}"}), 404
        else:
            return jsonify({"success": False, "error": "附件不存在"}), 404

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@material_management_bp.route('/api/open_local_file', methods=['POST'])
def open_local_file():
    """打开本地文件"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')

        if not file_path:
            return jsonify({"success": False, "error": "文件路径不能为空"}), 400

        if not os.path.exists(file_path):
            return jsonify({"success": False, "error": "文件不存在"}), 404

        # 根据操作系统使用不同的方法打开文件
        try:
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                os.system(f'open "{file_path}"')
            else:  # Linux
                os.system(f'xdg-open "{file_path}"')

            return jsonify({
                "success": True,
                "message": f"文件已打开: {os.path.basename(file_path)}"
            })
        except Exception as e:
            return jsonify({"success": False, "error": f"打开文件失败: {str(e)}"}), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
