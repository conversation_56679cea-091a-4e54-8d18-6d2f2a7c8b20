// QMS文件管理器 - 后台脚本
// 处理下载请求和与本地应用的通信

// 本地应用服务器配置
const LOCAL_SERVER_URL = 'http://localhost:8765';

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('QMS文件管理器扩展已安装');
  
  // 设置默认配置
  chrome.storage.local.set({
    localServerUrl: LOCAL_SERVER_URL,
    autoDownload: true,
    downloadPath: 'C:\\QMS1\\',
    lastConnected: null
  });
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'downloadFile') {
    handleDownloadRequest(request.data)
      .then(result => sendResponse(result))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // 保持消息通道开放
  }
  
  if (request.action === 'checkLocalApp') {
    checkLocalAppConnection()
      .then(result => sendResponse(result))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
  
  if (request.action === 'getSettings') {
    chrome.storage.local.get(['localServerUrl', 'autoDownload', 'downloadPath'], (result) => {
      sendResponse(result);
    });
    return true;
  }
  
  if (request.action === 'saveSettings') {
    chrome.storage.local.set(request.settings, () => {
      sendResponse({ success: true });
    });
    return true;
  }
});

// 处理下载请求
async function handleDownloadRequest(downloadData) {
  try {
    // 获取本地服务器URL
    const settings = await getStorageData(['localServerUrl']);
    const serverUrl = settings.localServerUrl || LOCAL_SERVER_URL;
    
    console.log('发送下载请求到本地应用:', downloadData);
    
    // 发送请求到本地应用
    const response = await fetch(`${serverUrl}/download`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(downloadData)
    });
    
    if (!response.ok) {
      throw new Error(`本地应用响应错误: ${response.status}`);
    }
    
    const result = await response.json();
    
    // 更新最后连接时间
    chrome.storage.local.set({
      lastConnected: new Date().toISOString()
    });
    
    return result;
    
  } catch (error) {
    console.error('下载请求失败:', error);
    
    // 如果本地应用不可用，尝试使用浏览器默认下载
    try {
      return await fallbackDownload(downloadData);
    } catch (fallbackError) {
      throw new Error(`本地应用不可用且浏览器下载失败: ${error.message}`);
    }
  }
}

// 备用下载方法（使用浏览器默认下载）
async function fallbackDownload(downloadData) {
  return new Promise((resolve, reject) => {
    chrome.downloads.download({
      url: downloadData.url,
      filename: downloadData.filename,
      saveAs: false
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
      } else {
        resolve({
          success: true,
          message: '文件已下载到浏览器默认下载文件夹',
          downloadId: downloadId,
          fallback: true
        });
      }
    });
  });
}

// 检查本地应用连接
async function checkLocalAppConnection() {
  try {
    const settings = await getStorageData(['localServerUrl']);
    const serverUrl = settings.localServerUrl || LOCAL_SERVER_URL;
    
    const response = await fetch(`${serverUrl}/ping`, {
      method: 'GET',
      timeout: 3000
    });
    
    return {
      success: true,
      connected: response.ok,
      serverUrl: serverUrl
    };
    
  } catch (error) {
    return {
      success: false,
      connected: false,
      error: error.message
    };
  }
}

// 获取存储数据的辅助函数
function getStorageData(keys) {
  return new Promise((resolve) => {
    chrome.storage.local.get(keys, resolve);
  });
}

// 监听下载完成事件
chrome.downloads.onChanged.addListener((downloadDelta) => {
  if (downloadDelta.state && downloadDelta.state.current === 'complete') {
    console.log('下载完成:', downloadDelta);
    
    // 通知content script下载完成
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'downloadComplete',
          downloadId: downloadDelta.id
        });
      }
    });
  }
});

// 扩展图标点击事件
chrome.action.onClicked.addListener((tab) => {
  // 检查是否在QMS页面
  if (tab.url && tab.url.includes('**************:5000')) {
    // 在QMS页面，显示状态信息
    chrome.tabs.sendMessage(tab.id, {
      action: 'showStatus'
    });
  } else {
    // 不在QMS页面，打开设置页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('popup.html')
    });
  }
});

// 定期检查本地应用连接状态
setInterval(async () => {
  try {
    const result = await checkLocalAppConnection();
    chrome.storage.local.set({
      localAppConnected: result.connected,
      lastCheck: new Date().toISOString()
    });
  } catch (error) {
    chrome.storage.local.set({
      localAppConnected: false,
      lastCheck: new Date().toISOString()
    });
  }
}, 30000); // 每30秒检查一次

// 处理网络请求拦截（如果需要）
chrome.webRequest.onBeforeRequest.addListener(
  function(details) {
    // 可以在这里拦截特定的下载请求
    console.log('请求拦截:', details.url);
  },
  {
    urls: ["http://**************:5000/material_management/api/material/*/attachments/*/download"]
  },
  ["requestBody"]
);
