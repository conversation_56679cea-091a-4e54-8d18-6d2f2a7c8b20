#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为待检物料表添加采购单号字段
"""

from db_config import get_db_connection
import os

def add_purchase_order_field():
    """为待检物料表添加采购单号字段"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("开始为待检物料表添加采购单号字段...")
        
        # 读取SQL文件
        sql_file_path = os.path.join('database', 'add_purchase_order_field.sql')
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句并执行
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for sql in sql_statements:
            if sql.upper().startswith('ALTER TABLE'):
                print(f"执行: {sql[:50]}...")
                cursor.execute(sql)
                print("✅ 字段添加成功")
            elif sql.upper().startswith('DESCRIBE'):
                print("📋 检查表结构:")
                cursor.execute(sql)
                columns = cursor.fetchall()
                for col in columns:
                    if isinstance(col, tuple):
                        print(f"   {col[0]} - {col[1]}")
                    else:
                        print(f"   {col}")
        
        conn.commit()
        print("✅ 采购单号字段添加完成！")
        
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
    
    return True

if __name__ == "__main__":
    print("=== 为待检物料表添加采购单号字段 ===")
    success = add_purchase_order_field()
    
    if success:
        print("\n🎉 字段添加完成！现在可以在批量导入中使用采购单号了。")
    else:
        print("\n❌ 字段添加失败，请检查错误信息。")
