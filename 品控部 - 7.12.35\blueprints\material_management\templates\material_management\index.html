{% extends "base.html" %}

{% block title %}物料管理 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    .material-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
    }
    
    .search-box {
        display: flex;
        gap: 5px;
        align-items: center;
        margin-bottom: 5px;
    }

    .search-input {
        width: 250px;
        height: 28px;
        padding: 4px 8px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 11px;
    }
    
    .btn {
        padding: 2px 5px;
        font-size: 11px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        margin: 0 1px;
        white-space: nowrap;
    }
    
    .btn-primary {
        background: #1976d2;
        color: white;
    }
    
    .btn-primary:hover {
        background: #1565c0;
    }
    
    .btn-success {
        background: #4caf50;
        color: white;
    }
    
    .btn-success:hover {
        background: #45a049;
    }
    
    .btn-warning {
        background: #ff9800;
        color: white;
    }
    
    .btn-warning:hover {
        background: #f57c00;
    }
    
    .btn-danger {
        background: #f44336;
        color: white;
    }
    
    .btn-danger:hover {
        background: #d32f2f;
    }
    
    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }
    
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 8px;
        margin-bottom: 10px;
    }

    .stats-card {
        padding: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 4px;
        text-align: center;
    }

    .stats-number {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 2px;
    }

    .stats-label {
        font-size: 11px;
        opacity: 0.9;
    }
    
    .material-table {
        font-size: 11px;
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
        margin-top: 5px;
        background: white;
        border-radius: 3px;
        overflow: hidden;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .material-table th {
        background: #f5f5f5;
        padding: 4px 6px;
        font-size: 11px;
        font-weight: 600;
        color: #333;
        border: 1px solid #e0e0e0;
        text-align: center;
        white-space: nowrap;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .material-table td {
        padding: 3px 6px;
        font-size: 11px;
        border: 1px solid #e0e0e0;
        vertical-align: middle;
        text-align: center;
        white-space: nowrap;
    }

    .material-table tbody tr {
        height: 24px;
        transition: background-color 0.2s;
    }

    .material-table tbody tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .material-table tbody tr:hover {
        background-color: #eaf2fd;
    }

    .material-number {
        color: #1976d2;
        font-weight: 600;
        font-family: 'Courier New', monospace;
    }
    
    .material-number {
        font-weight: 600;
        color: #1976d2;
    }
    
    .material-type-badge {
        padding: 1px 4px;
        border-radius: 2px;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        display: inline-block;
        min-width: 40px;
        line-height: 1.2;
    }
    
    .type-metal {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .type-plastic {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    
    .type-rubber {
        background: #e8f5e8;
        color: #388e3c;
    }
    
    .type-glass {
        background: #fff3e0;
        color: #f57c00;
    }
    
    .type-other {
        background: #f5f5f5;
        color: #666;
    }

    /* 物料类型标签样式 */
    .category-badge {
        padding: 1px 4px;
        border-radius: 2px;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        display: inline-block;
        min-width: 40px;
        line-height: 1.2;
    }

    .category-原材料 {
        background: #e3f2fd;
        color: #1976d2;
        border: 1px solid #bbdefb;
    }

    .category-半成品 {
        background: #fff3e0;
        color: #f57c00;
        border: 1px solid #ffcc02;
    }

    .category-成品 {
        background: #e8f5e8;
        color: #388e3c;
        border: 1px solid #c8e6c9;
    }

    .category-辅料 {
        background: #f3e5f5;
        color: #7b1fa2;
        border: 1px solid #e1bee7;
    }

    .category-包装材料 {
        background: #fce4ec;
        color: #c2185b;
        border: 1px solid #f8bbd9;
    }

    .category-工具 {
        background: #e0f2f1;
        color: #00695c;
        border: 1px solid #b2dfdb;
    }

    .category-其他 {
        background: #f5f5f5;
        color: #616161;
        border: 1px solid #e0e0e0;
    }

    /* 检验类型标签样式 */
    .inspection-badge {
        padding: 1px 4px;
        border-radius: 2px;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        display: inline-block;
        min-width: 35px;
        line-height: 1.2;
    }

    .inspection-抽样 {
        background: #e8f5e8;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    .inspection-全检 {
        background: #fff3e0;
        color: #ef6c00;
        border: 1px solid #ffcc02;
    }

    .inspection-免检 {
        background: #e3f2fd;
        color: #1565c0;
        border: 1px solid #bbdefb;
    }

    /* 内联编辑样式 */
    .editable-field {
        position: relative;
        cursor: pointer;
    }

    .editable-field:hover {
        background: #f5f5f5;
        border-radius: 3px;
    }

    .empty-value {
        color: #999;
        font-style: italic;
        font-size: 10px;
        padding: 1px 4px;
        border: 1px dashed #ccc;
        border-radius: 2px;
        display: inline-block;
        min-width: 40px;
        text-align: center;
        line-height: 1.2;
    }

    .empty-value:hover {
        background: #f0f0f0;
        border-color: #4a9eff;
        color: #4a9eff;
    }

    .edit-select {
        padding: 1px 3px;
        border: 1px solid #4a9eff;
        border-radius: 2px;
        font-size: 10px;
        background: white;
        min-width: 50px;
    }

    .edit-select:focus {
        outline: none;
        border-color: #357abd;
        box-shadow: 0 0 3px rgba(74, 158, 255, 0.3);
    }

    .save-btn, .cancel-btn {
        padding: 1px 4px;
        margin: 0 1px;
        border: none;
        border-radius: 2px;
        font-size: 10px;
        cursor: pointer;
    }

    .save-btn {
        background: #4caf50;
        color: white;
    }

    .cancel-btn {
        background: #f44336;
        color: white;
    }

    .edit-controls {
        margin-top: 2px;
        text-align: center;
    }
    
    .actions {
        display: flex;
        gap: 5px;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        margin-top: 8px;
        padding: 8px;
    }

    .page-btn {
        padding: 3px 8px;
        border: 1px solid #ddd;
        background: white;
        color: #333;
        text-decoration: none;
        border-radius: 3px;
        font-size: 11px;
    }

    .page-btn:hover {
        background: #f5f5f5;
    }

    .page-btn.active {
        background: #1976d2;
        color: white;
        border-color: #1976d2;
    }

    .page-info {
        font-size: 11px;
        color: #666;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px;
        color: #999;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #ddd;
    }

    /* 页面标题和物料总数样式 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .total-count-display {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        white-space: nowrap;
        transition: transform 0.2s ease;
    }

    .total-count-display:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .total-count-display i {
        margin-right: 8px;
        font-size: 18px;
    }

    .total-count-number {
        font-size: 18px;
        margin-left: 5px;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    }

    /* 快速搜索表单样式 - 参考抽样检验记录 */
    .quick-search-form {
        display: flex;
        align-items: center;
        margin-right: 10px;
        position: relative;
        border: 1px solid #1976d2;
        border-radius: 4px;
        overflow: hidden;
        background-color: #fff;
        box-sizing: border-box;
    }

    .quick-search-input {
        height: 32px;
        width: 200px;
        padding: 2px 8px;
        padding-right: 30px;
        border: none;
        font-size: 12px;
        outline: none;
        background: transparent;
        z-index: 1;
        max-width: none;
    }

    .quick-search-input::placeholder {
        color: #999;
    }

    .search-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #1976d2;
        font-size: 14px;
        cursor: pointer;
        z-index: 2;
        pointer-events: auto;
    }

    /* 快速新增按钮样式 */
    .quick-add-btn {
        background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        text-decoration: none;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
    }

    .quick-add-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        text-decoration: none;
        color: white;
    }

    .quick-add-btn i {
        font-size: 16px;
    }

    /* 按钮样式统一 - 参考抽样检验记录 */
    .btn {
        height: 32px;
        line-height: 30px;
        padding: 0 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 12px;
        transition: all 0.2s;
        vertical-align: middle;
        box-sizing: border-box;
        border: none;
        cursor: pointer;
        text-decoration: none;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
        min-width: 80px;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
    }

    .btn-success {
        background-color: #28a745;
        color: white;
        min-width: 80px;
    }

    .btn-success:hover {
        background-color: #218838;
        text-decoration: none;
        color: white;
    }

    .btn-primary {
        background-color: #1976d2;
        color: white;
        min-width: 80px;
    }

    .btn-primary:hover {
        background-color: #1565c0;
    }

    /* 按钮容器 */
    .buttons-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 20px;
        border: none;
        border-radius: 8px;
        width: 90%;
        max-width: 600px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }

    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
    }

    .close:hover,
    .close:focus {
        color: #000;
        text-decoration: none;
    }

    /* 高级搜索表单样式 */
    .two-column-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .form-label {
        width: 80px;
        font-size: 12px;
        color: #333;
        font-weight: 500;
        margin-right: 10px;
        text-align: right;
    }

    .form-input {
        flex: 1;
    }

    .form-control {
        width: 100%;
        height: 32px;
        padding: 4px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 12px;
        outline: none;
        transition: border-color 0.2s;
    }

    .form-control:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }

    .search-actions {
        text-align: center;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .header-left {
            width: 100%;
        }

        .header-right {
            width: 100%;
            justify-content: space-between;
            gap: 8px;
        }

        .total-count-display {
            font-size: 14px;
            padding: 8px 16px;
        }

        .total-count-number {
            font-size: 16px;
        }

        .quick-search-form {
            width: 100%;
            margin-right: 0;
            margin-bottom: 8px;
            height: 38px;
        }

        .quick-search-input {
            width: 100%;
            height: 38px;
            font-size: 13px;
        }

        .btn {
            height: 38px !important;
            font-size: 13px !important;
            width: 48%;
            margin: 0;
            padding: 0 10px;
        }

        .buttons-container {
            width: 100%;
            justify-content: space-between;
        }

        .two-column-layout {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .form-label {
            width: 60px;
            text-align: left;
        }
    }

    @media (max-width: 480px) {
        .header-right {
            flex-direction: column;
            align-items: stretch;
        }

        .quick-search-form {
            width: 100%;
        }

        .btn {
            width: 100% !important;
            margin-bottom: 5px;
        }

        .buttons-container {
            flex-direction: column;
            gap: 5px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="header-left">
        <h1>物料管理</h1>
    </div>
    <div class="header-right">
        <!-- 物料总数显示 -->
        <div class="total-count-display">
            <i class="fas fa-boxes"></i>
            物料总数: <span class="total-count-number">{{ total_count }}</span>
        </div>

        <!-- 搜索框 -->
        <div class="quick-search-form">
            <input type="text" id="quick-search-input" class="quick-search-input" placeholder="请输入料号/名称/规格/材质进行搜索...">
            <i class="fas fa-search search-icon" id="search-icon"></i>
        </div>

        <!-- 按钮区域 -->
        <div class="buttons-container">
            <button id="advanced-search-btn" class="btn btn-secondary">高级搜索</button>
            <a href="{{ url_for('material_management.new_material') }}" class="btn btn-success">新增物料</a>
        </div>
    </div>
</div>

<div class="material-container">

    <!-- 搜索结果提示 -->
    {% if search_count is not none %}
    <div style="background: #e3f2fd; border: 1px solid #bbdefb; border-radius: 4px; padding: 10px; margin-bottom: 15px; display: flex; align-items: center; justify-content: space-between;">
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-search" style="color: #1976d2;"></i>
            <span style="color: #1976d2; font-weight: 500;">
                {% if search_term %}
                    快速搜索 "{{ search_term }}" 找到 {{ search_count }} 条结果
                {% else %}
                    高级搜索找到 {{ search_count }} 条结果
                {% endif %}
            </span>
        </div>
        <div style="display: flex; align-items: center; gap: 10px;">
            {% if not search_term %}
            <button onclick="document.getElementById('advanced-search-modal').style.display='block'" style="background: none; border: none; color: #1976d2; text-decoration: none; font-size: 12px; display: flex; align-items: center; gap: 4px; cursor: pointer;">
                <i class="fas fa-edit"></i> 修改条件
            </button>
            {% endif %}
            <a href="{{ url_for('material_management.index') }}" style="color: #1976d2; text-decoration: none; font-size: 12px; display: flex; align-items: center; gap: 4px;">
                <i class="fas fa-times"></i> 清空搜索
            </a>
        </div>
    </div>
    {% endif %}

    <!-- 物料表格 -->
    <div class="table-responsive">
        <table class="material-table">
            <thead>
                <tr>
                    <th width="12%">物料料号</th>
                    <th width="18%">物料名称</th>
                    <th width="12%">规格</th>
                    <th width="10%">材质</th>
                    <th width="6%">颜色</th>
                    <th width="8%">物料类型</th>
                    <th width="8%">检验类型</th>
                    <th width="8%">创建时间</th>
                    <th width="18%">操作</th>
                </tr>
            </thead>
            <tbody id="materials-tbody">
                {% if materials %}
                    {% for material in materials %}
                    <tr>
                        <td class="material-number">{{ material.material_number }}</td>
                        <td>{{ material.material_name }}</td>
                        <td>{{ material.specification or '-' }}</td>
                        <td>
                            {% if material.material_type %}
                                <span class="material-type-badge {% if '钢' in material.material_type or '铝' in material.material_type or '铜' in material.material_type or '钛' in material.material_type or '镁' in material.material_type %}type-metal{% elif '塑料' in material.material_type or 'ABS' in material.material_type or '尼龙' in material.material_type %}type-plastic{% elif '橡胶' in material.material_type or '硅' in material.material_type %}type-rubber{% elif '玻璃' in material.material_type or '陶瓷' in material.material_type %}type-glass{% else %}type-other{% endif %}">
                                    {{ material.material_type }}
                                </span>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ material.color or '-' }}</td>
                        <td>
                            <div class="editable-field" data-field="material_category" data-material-id="{{ material.id }}">
                                {% if material.material_category %}
                                    <span class="category-badge category-{{ material.material_category }}" onclick="editField(this)">
                                        {{ material.material_category }}
                                    </span>
                                {% else %}
                                    <span class="empty-value" onclick="editField(this)">点击设置</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="editable-field" data-field="inspection_type" data-material-id="{{ material.id }}">
                                {% if material.inspection_type %}
                                    <span class="inspection-badge inspection-{{ material.inspection_type }}" onclick="editField(this)">
                                        {{ material.inspection_type }}
                                    </span>
                                {% else %}
                                    <span class="empty-value" onclick="editField(this)">点击设置</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ material.created_at.strftime('%Y-%m-%d') if material.created_at else '-' }}</td>
                        <td>
                            <div class="actions">
                                <a href="{{ url_for('material_management.view_material', material_id=material.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                                <a href="{{ url_for('material_management.edit_material', material_id=material.id) }}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <button class="btn btn-danger btn-sm" onclick="deleteMaterial({{ material.id }}, '{{ material.material_number }}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="7">
                            <div class="empty-state">
                                <i class="fas fa-box-open"></i>
                                <div>暂无物料数据</div>
                                <div style="margin-top: 10px;">
                                    <a href="{{ url_for('material_management.new_material') }}" class="btn btn-success">
                                        <i class="fas fa-plus"></i> 添加第一个物料
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination" id="pagination">
        <!-- 分页内容将通过JavaScript动态生成 -->
    </div>
</div>

<!-- 高级搜索模态框 -->
<div id="advanced-search-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="document.getElementById('advanced-search-modal').style.display='none'">&times;</span>
        <h2 style="font-size: 13px; margin: 0 0 10px 0; border-bottom: 1px solid #eee; padding-bottom: 3px;">高级搜索</h2>

        <form id="advanced-search-form" method="get" action="{{ url_for('material_management.index') }}">
            <div class="two-column-layout">
                <!-- 左侧列 -->
                <div class="column left-column">
                    <div class="form-group">
                        <div class="form-label">物料料号</div>
                        <div class="form-input">
                            <input type="text" name="material_number" value="{{ material_number or '' }}" class="form-control" placeholder="输入物料料号">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">物料名称</div>
                        <div class="form-input">
                            <input type="text" name="material_name" value="{{ material_name or '' }}" class="form-control" placeholder="输入物料名称">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">规格</div>
                        <div class="form-input">
                            <input type="text" name="specification" value="{{ specification or '' }}" class="form-control" placeholder="输入规格">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">材质</div>
                        <div class="form-input">
                            <input type="text" name="material_type" value="{{ material_type or '' }}" class="form-control" placeholder="输入材质">
                        </div>
                    </div>
                </div>

                <!-- 右侧列 -->
                <div class="column right-column">
                    <div class="form-group">
                        <div class="form-label">颜色</div>
                        <div class="form-input">
                            <input type="text" name="color" value="{{ color or '' }}" class="form-control" placeholder="输入颜色">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">物料类型</div>
                        <div class="form-input">
                            <select name="material_category" class="form-control">
                                <option value="">全部类型</option>
                                <option value="原材料" {{ 'selected' if material_category == '原材料' else '' }}>原材料</option>
                                <option value="半成品" {{ 'selected' if material_category == '半成品' else '' }}>半成品</option>
                                <option value="成品" {{ 'selected' if material_category == '成品' else '' }}>成品</option>
                                <option value="辅料" {{ 'selected' if material_category == '辅料' else '' }}>辅料</option>
                                <option value="包装材料" {{ 'selected' if material_category == '包装材料' else '' }}>包装材料</option>
                                <option value="工具" {{ 'selected' if material_category == '工具' else '' }}>工具</option>
                                <option value="其他" {{ 'selected' if material_category == '其他' else '' }}>其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">检验类型</div>
                        <div class="form-input">
                            <select name="inspection_type" class="form-control">
                                <option value="">全部类型</option>
                                <option value="抽样" {{ 'selected' if inspection_type == '抽样' else '' }}>抽样检验</option>
                                <option value="全检" {{ 'selected' if inspection_type == '全检' else '' }}>全部检验</option>
                                <option value="免检" {{ 'selected' if inspection_type == '免检' else '' }}>免检</option>
                            </select>
                        </div>
                    </div>

                </div>
            </div>

            <div class="search-actions">
                <button type="submit" class="btn btn-primary" style="margin-right: 10px;">搜索</button>
                <button type="button" class="btn btn-secondary" onclick="resetAdvancedSearch()">重置</button>
                <button type="button" class="btn btn-secondary" onclick="document.getElementById('advanced-search-modal').style.display='none'" style="margin-left: 10px;">取消</button>
            </div>
        </form>
    </div>
</div>

<script>
    // 获取搜索相关元素
    const quickSearchInput = document.getElementById('quick-search-input');
    const searchIcon = document.getElementById('search-icon');
    const advancedSearchBtn = document.getElementById('advanced-search-btn');

    // 定义执行搜索的函数
    function performSearch() {
        const searchTerm = quickSearchInput.value.trim();
        if (searchTerm) {
            // 显示加载状态
            searchIcon.className = 'fas fa-spinner fa-spin search-icon';

            // 延迟一下显示加载效果
            setTimeout(() => {
                window.location.href = `{{ url_for('material_management.index') }}?search=${encodeURIComponent(searchTerm)}`;
            }, 300);
        } else {
            // 清空搜索
            window.location.href = `{{ url_for('material_management.index') }}`;
        }
    }

    // 搜索图标点击事件
    searchIcon.addEventListener('click', performSearch);

    // 快速搜索回车键
    quickSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 高级搜索按钮点击事件
    advancedSearchBtn.addEventListener('click', function() {
        document.getElementById('advanced-search-modal').style.display = 'block';
    });
    
    // 重置高级搜索表单
    function resetAdvancedSearch() {
        const form = document.getElementById('advanced-search-form');
        form.reset();
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 如果URL中有搜索参数，显示在搜索框中
        const urlParams = new URLSearchParams(window.location.search);
        const searchParam = urlParams.get('search');
        if (searchParam) {
            quickSearchInput.value = searchParam;
        }

        // 模态框点击外部关闭
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('advanced-search-modal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('advanced-search-modal');
                if (modal.style.display === 'block') {
                    modal.style.display = 'none';
                }
            }
        });
    });
    
    // 删除物料
    function deleteMaterial(materialId, materialNumber) {
        if (confirm(`确定要删除物料 "${materialNumber}" 吗？\n\n注意：如果该物料存在检验记录，将无法删除。`)) {
            fetch(`/material_management/api/material/${materialId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('物料删除成功');
                    location.reload();
                } else {
                    alert('删除失败: ' + data.error);
                }
            })
            .catch(error => {
                alert('删除失败: ' + error);
                console.error('删除错误:', error);
            });
        }
    }

    // 内联编辑功能
    function editField(element) {
        const container = element.closest('.editable-field');
        const field = container.dataset.field;
        const materialId = container.dataset.materialId;
        const currentValue = element.textContent.trim();

        // 如果已经在编辑状态，不重复创建
        if (container.querySelector('.edit-select')) {
            return;
        }

        // 创建下拉选择框
        const select = document.createElement('select');
        select.className = 'edit-select';

        let options = [];
        if (field === 'material_category') {
            options = [
                { value: '', text: '请选择物料类型' },
                { value: '原材料', text: '原材料' },
                { value: '半成品', text: '半成品' },
                { value: '成品', text: '成品' },
                { value: '辅料', text: '辅料' },
                { value: '包装材料', text: '包装材料' },
                { value: '工具', text: '工具' },
                { value: '其他', text: '其他' }
            ];
        } else if (field === 'inspection_type') {
            options = [
                { value: '', text: '请选择检验类型' },
                { value: '抽样', text: '抽样检验' },
                { value: '全检', text: '全部检验' },
                { value: '免检', text: '免检' }
            ];
        }

        // 添加选项
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            if (option.value === currentValue || (currentValue === '点击设置' && option.value === '')) {
                optionElement.selected = true;
            }
            select.appendChild(optionElement);
        });

        // 创建控制按钮
        const controls = document.createElement('div');
        controls.className = 'edit-controls';

        const saveBtn = document.createElement('button');
        saveBtn.className = 'save-btn';
        saveBtn.textContent = '保存';
        saveBtn.onclick = () => saveField(materialId, field, select.value, container);

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'cancel-btn';
        cancelBtn.textContent = '取消';
        cancelBtn.onclick = () => cancelEdit(container);

        controls.appendChild(saveBtn);
        controls.appendChild(cancelBtn);

        // 替换原内容
        container.innerHTML = '';
        container.appendChild(select);
        container.appendChild(controls);

        // 聚焦到选择框
        select.focus();

        // 添加键盘事件
        select.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                saveField(materialId, field, select.value, container);
            } else if (e.key === 'Escape') {
                cancelEdit(container);
            }
        });
    }

    // 保存字段
    function saveField(materialId, field, value, container) {
        if (!value) {
            alert('请选择一个值');
            return;
        }

        // 显示加载状态
        container.innerHTML = '<span style="color: #999;">保存中...</span>';

        // 发送更新请求
        fetch(`/material_management/api/material/${materialId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                [field]: value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新显示
                updateFieldDisplay(container, field, value);
                showMessage('更新成功', 'success');
            } else {
                showMessage('更新失败: ' + data.error, 'error');
                cancelEdit(container);
            }
        })
        .catch(error => {
            console.error('更新失败:', error);
            showMessage('更新失败: ' + error.message, 'error');
            cancelEdit(container);
        });
    }

    // 取消编辑
    function cancelEdit(container) {
        // 重新加载页面以恢复原始状态
        location.reload();
    }

    // 更新字段显示
    function updateFieldDisplay(container, field, value) {
        let badgeClass = '';
        let displayText = value;

        if (field === 'material_category') {
            badgeClass = `category-badge category-${value}`;
        } else if (field === 'inspection_type') {
            badgeClass = `inspection-badge inspection-${value}`;
        }

        container.innerHTML = `
            <span class="${badgeClass}" onclick="editField(this)">
                ${displayText}
            </span>
        `;
    }

    // 显示消息
    function showMessage(message, type) {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            padding: 10px 15px; border-radius: 4px; color: white;
            background: ${type === 'success' ? '#4caf50' : '#f44336'};
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }
</script>
{% endblock %}
