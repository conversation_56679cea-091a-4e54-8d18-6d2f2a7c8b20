<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QMS文件管理器</title>
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.9;
        }

        .content {
            padding: 20px;
            background: white;
        }

        .status-section {
            margin-bottom: 20px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-size: 14px;
            color: #333;
        }

        .status-value {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-disconnected {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-error {
            background: #ffebee;
            color: #c62828;
        }

        .settings-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .input-field {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .input-field:focus {
            outline: none;
            border-color: #4caf50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .checkbox-group label {
            font-size: 14px;
            color: #333;
            cursor: pointer;
        }

        .button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
            margin-bottom: 10px;
        }

        .button:hover {
            background: #45a049;
        }

        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .button-secondary {
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }

        .button-secondary:hover {
            background: #e0e0e0;
        }

        .actions-section {
            margin-top: 20px;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            font-size: 12px;
            color: #1565c0;
        }

        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4caf50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 11px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>QMS文件管理器</h1>
        <p>智能文件下载管理扩展</p>
    </div>

    <div class="content">
        <div class="status-section">
            <div class="section-title">连接状态</div>
            
            <div class="status-item">
                <span class="status-label">本地应用</span>
                <span id="local-app-status" class="status-value status-disconnected">
                    <span class="loading"></span> 检查中...
                </span>
            </div>
            
            <div class="status-item">
                <span class="status-label">QMS服务器</span>
                <span id="qms-server-status" class="status-value status-disconnected">
                    <span class="loading"></span> 检查中...
                </span>
            </div>
            
            <div class="status-item">
                <span class="status-label">下载次数</span>
                <span id="download-count" class="status-value">0</span>
            </div>
        </div>

        <div class="settings-section">
            <div class="section-title">设置</div>
            
            <div class="input-group">
                <label class="input-label" for="download-path">默认下载路径</label>
                <input type="text" id="download-path" class="input-field" placeholder="C:\QMS1\" />
            </div>
            
            <div class="input-group">
                <label class="input-label" for="local-server-url">本地应用地址</label>
                <input type="text" id="local-server-url" class="input-field" placeholder="http://localhost:8765" />
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="auto-download" />
                <label for="auto-download">自动下载到指定路径</label>
            </div>
        </div>

        <div class="actions-section">
            <button id="save-settings" class="button">保存设置</button>
            <button id="test-connection" class="button button-secondary">测试连接</button>
            <button id="open-local-app" class="button button-secondary">打开本地应用</button>
        </div>

        <div class="info-box">
            <strong>使用说明：</strong><br>
            1. 确保QMS文件管理器应用程序正在运行<br>
            2. 在QMS网页上点击附件下载<br>
            3. 文件将自动下载到指定路径并打开
        </div>
    </div>

    <div class="footer">
        QMS文件管理器 v1.0.0 | 品质中心专用
    </div>

    <script src="popup.js"></script>
</body>
</html>
