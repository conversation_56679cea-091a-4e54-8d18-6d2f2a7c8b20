#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
为抽样检验表添加检验类型字段
"""

from db_config import get_db_connection

def add_inspection_type_field():
    """添加检验类型字段到抽样检验表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'quality_control' 
            AND TABLE_NAME = 'sampling_inspection' 
            AND COLUMN_NAME = 'inspection_type'
        """)
        
        if cursor.fetchone():
            print("✅ inspection_type 字段已存在")
            return True
            
        # 添加检验类型字段
        cursor.execute("""
            ALTER TABLE sampling_inspection 
            ADD COLUMN inspection_type ENUM('sampling', 'full', 'exempt') 
            DEFAULT 'sampling' 
            COMMENT '检验类型：sampling-抽样检验，full-全部检验，exempt-免检'
            AFTER batch_number
        """)
        
        # 为现有记录设置默认值
        cursor.execute("""
            UPDATE sampling_inspection 
            SET inspection_type = 'sampling' 
            WHERE inspection_type IS NULL
        """)
        
        # 添加索引
        cursor.execute("""
            ALTER TABLE sampling_inspection 
            ADD INDEX idx_inspection_type (inspection_type)
        """)
        
        conn.commit()
        print("✅ inspection_type 字段添加成功")
        return True
        
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    add_inspection_type_field()
