# ===================================
# 品质中心管理系统 - 环境变量配置模板
# ===================================
# 复制此文件为 .env 并修改相应的值

# 应用环境
FLASK_ENV=development
FLASK_DEBUG=True

# 安全密钥（生产环境请使用随机生成的强密钥）
SECRET_KEY=your-secret-key-change-this-in-production

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your-database-password
DB_NAME=quality_control

# 文件上传配置
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216

# 分页配置
ITEMS_PER_PAGE=20

# 会话配置
PERMANENT_SESSION_LIFETIME=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 邮件配置（如果需要）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# 备份配置
BACKUP_PATH=backups/
BACKUP_RETENTION_DAYS=30

# 文档路径
DOCUMENT_PATH=DocumentsMaterials
