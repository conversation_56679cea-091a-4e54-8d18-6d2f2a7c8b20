<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>触发时机优化 - 品质中心管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .optimization-section {
            margin: 30px 0;
            padding: 20px;
            background: #f0fff4;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #28a745;
            margin-bottom: 15px;
        }
        .trigger-demo {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        .demo-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #2d3748;
        }
        .demo-desc {
            color: #4a5568;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .demo-table {
            font-size: 11px;
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .demo-table th, .demo-table td {
            padding: 3px 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
            vertical-align: middle;
        }
        .demo-table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        .demo-table input {
            width: 100%;
            padding: 2px 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 11px;
            height: 24px;
            box-sizing: border-box;
        }
        .trigger-method {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .code-block {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .highlight-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        .before-card {
            background: #fff5f5;
            border-left: 4px solid #dc3545;
        }
        .after-card {
            background: #f0fff4;
            border-left: 4px solid #28a745;
        }
        .card-title {
            font-weight: 600;
            margin-bottom: 10px;
        }
        .before-title {
            color: #dc3545;
        }
        .after-title {
            color: #28a745;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #28a745;
        }
        .feature-list li::before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .usage-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            margin: 15px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #1976d2;
        }
        .step-number {
            font-weight: 600;
            color: #1976d2;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ 物料信息自动获取触发时机优化</h1>
        <p>优化批量导入页面中物料信息自动获取的触发时机，提供多种触发方式确保用户体验</p>

        <div class="optimization-section">
            <div class="section-title">✅ 问题解决</div>
            <p>您提到的触发时机问题已经解决！现在提供了多种触发方式，确保用户在离开文本框后能够可靠地触发自动获取功能。</p>
        </div>

        <div class="comparison-grid">
            <div class="comparison-card before-card">
                <div class="card-title before-title">优化前：单一触发方式</div>
                <p><strong>触发方式：</strong></p>
                <div class="trigger-method">blur (失去焦点)</div>
                <p><strong>问题：</strong></p>
                <ul>
                    <li>只有失去焦点时才触发</li>
                    <li>用户可能不知道需要点击其他地方</li>
                    <li>触发时机不够灵活</li>
                    <li>可能出现重复调用</li>
                </ul>
            </div>
            
            <div class="comparison-card after-card">
                <div class="card-title after-title">优化后：多种触发方式</div>
                <p><strong>触发方式：</strong></p>
                <div class="trigger-method">blur (失去焦点)</div>
                <div class="trigger-method">Enter键</div>
                <div class="trigger-method">Tab键</div>
                <div class="trigger-method">输入停顿2秒</div>
                <p><strong>优势：</strong></p>
                <ul>
                    <li>多种触发方式，用户体验更好</li>
                    <li>自动防重复调用</li>
                    <li>智能缓存已获取的信息</li>
                    <li>详细的调试日志</li>
                </ul>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 新的触发方式详解</h3>
            
            <div class="trigger-demo">
                <div class="demo-title">1. 失去焦点触发 (blur)</div>
                <div class="demo-desc">用户点击其他地方或切换到其他输入框时触发</div>
                <div class="code-block">
input.addEventListener('blur', function() {
    console.log('🎯 触发事件：blur (失去焦点)');
    fetchMaterialInfo(this);
});
                </div>
            </div>

            <div class="trigger-demo">
                <div class="demo-title">2. Enter键触发</div>
                <div class="demo-desc">用户输入料号后按Enter键立即触发</div>
                <div class="code-block">
input.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        console.log('🎯 触发事件：Enter键');
        e.preventDefault(); // 防止表单提交
        this.blur(); // 触发失去焦点，然后自动获取
    }
});
                </div>
            </div>

            <div class="trigger-demo">
                <div class="demo-title">3. Tab键触发</div>
                <div class="demo-desc">用户按Tab键切换到下一个输入框时自然触发blur事件</div>
                <div class="code-block">
input.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
        console.log('🎯 触发事件：Tab键');
        // Tab键会自然触发blur事件
    }
});
                </div>
            </div>

            <div class="trigger-demo">
                <div class="demo-title">4. 输入停顿自动触发</div>
                <div class="demo-desc">用户输入料号后停顿2秒自动触发（防抖处理）</div>
                <div class="code-block">
let debounceTimer;
input.addEventListener('input', function() {
    const currentValue = this.value.trim();
    
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }
    
    if (currentValue.length > 0) {
        debounceTimer = setTimeout(() => {
            console.log('🎯 触发事件：输入停顿2秒后自动触发');
            fetchMaterialInfo(this);
        }, 2000);
    }
});
                </div>
            </div>
        </div>

        <div class="optimization-section">
            <div class="section-title">🛡️ 防重复调用机制</div>
            <p>为了避免重复调用API，添加了智能防重复机制：</p>
            
            <div class="code-block">
// 防止重复调用
if (input.dataset.fetching === 'true') {
    console.log('⚠️ 正在获取中，跳过重复调用');
    return;
}

// 检查是否已经获取过相同的料号
if (currentName && currentSpec && input.dataset.lastFetched === materialCode) {
    console.log('✅ 该料号信息已获取，跳过重复获取');
    return;
}

// 标记正在获取
input.dataset.fetching = 'true';

// ... API调用 ...

// 获取完成后清除标记
input.dataset.fetching = 'false';
input.dataset.lastFetched = materialCode;
            </div>
        </div>

        <div class="highlight-box">
            <h3>📋 使用方法</h3>
            <div class="usage-steps">
                <div class="step">
                    <span class="step-number">方式1：</span>
                    输入料号后，点击其他地方（失去焦点）
                </div>
                <div class="step">
                    <span class="step-number">方式2：</span>
                    输入料号后，按Enter键
                </div>
                <div class="step">
                    <span class="step-number">方式3：</span>
                    输入料号后，按Tab键切换到下一个输入框
                </div>
                <div class="step">
                    <span class="step-number">方式4：</span>
                    输入料号后，等待2秒自动触发
                </div>
            </div>
        </div>

        <div class="trigger-demo">
            <div class="demo-title">📊 实际效果演示</div>
            <div class="demo-desc">以下表格展示了优化后的触发效果：</div>
            <table class="demo-table">
                <thead>
                    <tr>
                        <th>物料料号 *</th>
                        <th>物料名称</th>
                        <th>规格</th>
                        <th>供应商</th>
                        <th>来料数量</th>
                        <th>单位</th>
                        <th>批次号</th>
                        <th>到货日期</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="text" placeholder="输入料号后按Enter或Tab或等待2秒"></td>
                        <td><input type="text" placeholder="自动获取" style="background: #e8f5e8;"></td>
                        <td><input type="text" placeholder="自动获取" style="background: #e8f5e8;"></td>
                        <td><input type="text" placeholder="自动获取" style="background: #e8f5e8;"></td>
                        <td><input type="number" placeholder="手动输入"></td>
                        <td><input type="text" placeholder="手动输入"></td>
                        <td><input type="text" placeholder="手动输入"></td>
                        <td><input type="date" placeholder="手动输入"></td>
                        <td><button style="padding: 1px 3px; font-size: 10px;">删除</button></td>
                    </tr>
                </tbody>
            </table>
            <p style="color: #28a745; font-size: 12px;">
                💡 绿色背景的字段表示会自动获取的信息
            </p>
        </div>

        <div class="optimization-section">
            <div class="section-title">🎉 优化成果</div>
            <ul class="feature-list">
                <li>支持4种不同的触发方式，满足不同用户习惯</li>
                <li>智能防重复调用，避免不必要的API请求</li>
                <li>缓存已获取的信息，提高响应速度</li>
                <li>详细的调试日志，便于问题排查</li>
                <li>防抖处理，避免频繁触发</li>
                <li>用户友好的错误提示和成功反馈</li>
            </ul>
        </div>

        <div class="highlight-box">
            <h3>🔍 调试信息</h3>
            <p>现在在浏览器控制台中可以看到详细的触发信息：</p>
            <div class="code-block">
🎯 触发事件：Enter键
🔍 开始获取物料信息: ABC001
📡 请求物料信息URL: /api/material_info/ABC001
📡 物料信息响应状态: 200
📋 物料信息响应数据: {success: true, material: {...}}
✅ 物料信息获取成功: {...}
✅ 物料基本信息已填充
📡 请求供应商信息URL: /incoming/api/recent_supplier/ABC001
📡 供应商信息响应状态: 200
✅ 供应商信息已填充: ABC电子
🏁 物料信息获取完成，清除获取标记
            </div>
        </div>

        <div class="optimization-section">
            <div class="section-title">✅ 问题解决确认</div>
            <p><strong>您提到的触发时机问题已经完全解决！</strong></p>
            <p>现在用户可以通过以下任何方式触发物料信息自动获取：</p>
            <ul>
                <li>✅ 输入料号后点击其他地方（原有方式）</li>
                <li>✅ 输入料号后按Enter键（新增）</li>
                <li>✅ 输入料号后按Tab键切换（新增）</li>
                <li>✅ 输入料号后等待2秒自动触发（新增）</li>
            </ul>
            <p>这样确保了无论用户采用什么操作习惯，都能可靠地触发自动获取功能！</p>
        </div>
    </div>
</body>
</html>
