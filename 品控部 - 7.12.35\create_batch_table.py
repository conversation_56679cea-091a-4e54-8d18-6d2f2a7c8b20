#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建pending_inspection_batches表
"""

from db_config import get_db_connection

def create_batch_table():
    """创建待检物料批次表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 创建待检物料批次表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS pending_inspection_batches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                batch_name VARCHAR(100) NOT NULL COMMENT '批次名称',
                inspection_type ENUM('sampling', 'full', 'exempt') NOT NULL COMMENT '检验类型',
                total_items INT DEFAULT 0 COMMENT '总物料数',
                pending_items INT DEFAULT 0 COMMENT '待检数量',
                in_progress_items INT DEFAULT 0 COMMENT '检验中数量',
                completed_items INT DEFAULT 0 COMMENT '已完成数量',
                created_by VARCHA<PERSON>(50) COMMENT '创建人',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                INDEX idx_inspection_type (inspection_type),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待检物料批次表'
        """)
        
        print('✅ pending_inspection_batches表创建成功')
        
        # 检查pending_inspections表是否有batch_id字段
        cursor.execute('DESCRIBE pending_inspections')
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        if 'batch_id' not in column_names:
            cursor.execute('ALTER TABLE pending_inspections ADD COLUMN batch_id INT COMMENT "批次ID"')
            cursor.execute('ALTER TABLE pending_inspections ADD INDEX idx_batch_id (batch_id)')
            print('✅ 添加batch_id字段成功')
        else:
            print('✅ batch_id字段已存在')
        
        conn.commit()
        cursor.close()
        conn.close()
        print('✅ 表结构准备完成')
        return True
        
    except Exception as e:
        print(f'❌ 错误: {e}')
        return False

if __name__ == "__main__":
    create_batch_table()
