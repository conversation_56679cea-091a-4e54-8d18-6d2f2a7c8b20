{% extends "base.html" %}

{% block title %}新增物料 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    .material-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .form-section {
        background: #fafafa;
        border-radius: 6px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        margin-bottom: 20px;
    }
    
    .form-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #1976d2;
        display: flex;
        align-items: center;
    }
    
    .form-section-title i {
        margin-right: 8px;
        font-size: 18px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-label {
        display: block;
        font-size: 13px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
    }
    
    .form-label.required::after {
        content: " *";
        color: #f44336;
    }
    
    .form-input {
        width: 100%;
        height: 36px;
        padding: 8px 12px;
        font-size: 13px;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.3s ease;
        box-sizing: border-box;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #1976d2;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
    }
    
    .form-textarea {
        height: 80px;
        resize: vertical;
    }
    
    .form-help {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    
    .btn-group {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 13px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }
    
    .btn-primary {
        background: #1976d2;
        color: white;
    }
    
    .btn-primary:hover {
        background: #1565c0;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
    }
    
    .alert {
        padding: 12px 15px;
        margin-bottom: 20px;
        border-radius: 4px;
        font-size: 13px;
    }
    
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .alert-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .material-form-container {
            margin: 10px;
            padding: 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>新增物料</h1>
</div>

<div class="material-form-container">
    <form id="material-form">
        <div class="form-section">
            <div class="form-section-title">
                <i class="fas fa-box"></i>
                基本信息
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label required" for="material_number">物料料号</label>
                    <input type="text" id="material_number" name="material_number" class="form-input" required placeholder="请输入物料料号">
                    <div class="form-help">物料的唯一标识，不可重复</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label required" for="material_name">物料名称</label>
                    <input type="text" id="material_name" name="material_name" class="form-input" required placeholder="请输入物料名称">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="specification">规格</label>
                    <input type="text" id="specification" name="specification" class="form-input" placeholder="请输入规格尺寸">
                    <div class="form-help">例如：100x50x20mm、Φ25x8mm等</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="material_type">材质</label>
                    <input type="text" id="material_type" name="material_type" class="form-input" placeholder="请输入材质">
                    <div class="form-help">例如：304不锈钢、6061铝合金、ABS塑料等</div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="color">颜色</label>
                    <input type="text" id="color" name="color" class="form-input" placeholder="请输入颜色">
                    <div class="form-help">例如：银色、黑色、透明等</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="material_category">物料类型</label>
                    <select id="material_category" name="material_category" class="form-input">
                        <option value="">请选择物料类型</option>
                        <option value="原材料">原材料</option>
                        <option value="半成品">半成品</option>
                        <option value="成品">成品</option>
                        <option value="辅料">辅料</option>
                        <option value="包装材料">包装材料</option>
                        <option value="工具">工具</option>
                        <option value="其他">其他</option>
                    </select>
                    <div class="form-help">选择物料的分类类型</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="inspection_type">检验类型</label>
                    <select id="inspection_type" name="inspection_type" class="form-input">
                        <option value="">请选择检验类型</option>
                        <option value="抽样">抽样检验</option>
                        <option value="全检">全部检验</option>
                        <option value="免检">免检</option>
                    </select>
                    <div class="form-help">设置该物料的默认检验方式</div>
                </div>

                <div class="form-group">
                    <!-- 占位，保持布局对齐 -->
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="description">备注说明</label>
                <textarea id="description" name="description" class="form-input form-textarea" placeholder="请输入备注说明（可选）"></textarea>
                <div class="form-help">可以填写物料的用途、特殊要求等信息</div>
            </div>
        </div>
        
        <div class="btn-group">
            <a href="{{ url_for('material_management.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存物料
            </button>
        </div>
    </form>
</div>

<script>
    document.getElementById('material-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 获取表单数据
        const formData = {
            material_number: document.getElementById('material_number').value.trim(),
            material_name: document.getElementById('material_name').value.trim(),
            specification: document.getElementById('specification').value.trim(),
            material_type: document.getElementById('material_type').value.trim(),
            color: document.getElementById('color').value.trim(),
            description: document.getElementById('description').value.trim()
        };
        
        // 验证必填字段
        if (!formData.material_number) {
            showAlert('请输入物料料号', 'error');
            document.getElementById('material_number').focus();
            return;
        }
        
        if (!formData.material_name) {
            showAlert('请输入物料名称', 'error');
            document.getElementById('material_name').focus();
            return;
        }
        
        // 禁用提交按钮
        const submitBtn = document.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        
        // 发送请求
        fetch('/material_management/api/material', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('物料保存成功！', 'success');
                // 延迟跳转，让用户看到成功提示
                setTimeout(() => {
                    window.location.href = '{{ url_for("material_management.index") }}';
                }, 1500);
            } else {
                showAlert('保存失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showAlert('保存失败: ' + error, 'error');
            console.error('保存错误:', error);
        })
        .finally(() => {
            // 恢复提交按钮
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save"></i> 保存物料';
        });
    });
    
    // 显示提示信息
    function showAlert(message, type) {
        // 移除现有的提示
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // 创建新的提示
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        // 插入到表单顶部
        const form = document.getElementById('material-form');
        form.insertBefore(alert, form.firstChild);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
</script>
{% endblock %}
