#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建待检物料相关数据库表
"""

from db_config import get_db_connection
import os

def create_pending_inspection_tables():
    """创建待检物料相关表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("开始创建待检物料相关表...")
        
        # 读取SQL文件
        sql_file_path = os.path.join('database', 'create_pending_inspection_table.sql')
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句（按分号分割）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        # 执行每个SQL语句
        for i, sql in enumerate(sql_statements):
            try:
                print(f"执行SQL语句 {i+1}/{len(sql_statements)}...")
                cursor.execute(sql)
                conn.commit()
                print(f"✅ SQL语句 {i+1} 执行成功")
            except Exception as e:
                print(f"❌ SQL语句 {i+1} 执行失败: {e}")
                # 如果是外键约束错误，可能是因为表已存在，继续执行
                if "foreign key constraint" in str(e).lower() or "duplicate" in str(e).lower():
                    print("   (可能是表已存在，继续执行...)")
                    continue
                else:
                    raise e
        
        print("\n✅ 所有表创建完成！")
        
        # 验证表是否创建成功
        print("\n验证表创建结果:")
        cursor.execute("SHOW TABLES LIKE 'pending_%'")
        tables = cursor.fetchall()
        
        if tables:
            print("✅ 成功创建的表:")
            for table in tables:
                print(f"   - {table[0]}")
        else:
            print("❌ 没有找到待检相关表")
        
        # 检查表结构
        print("\n检查表结构:")
        for table_name in ['pending_inspections', 'pending_inspection_batches']:
            try:
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                print(f"\n📋 {table_name} 表结构:")
                for col in columns:
                    print(f"   {col[0]} - {col[1]}")
            except Exception as e:
                print(f"❌ 无法获取 {table_name} 表结构: {e}")
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
    
    return True

if __name__ == "__main__":
    print("=== 创建待检物料数据库表 ===")
    success = create_pending_inspection_tables()
    
    if success:
        print("\n🎉 数据库表创建完成！现在可以启动应用了。")
    else:
        print("\n❌ 数据库表创建失败，请检查错误信息。")
