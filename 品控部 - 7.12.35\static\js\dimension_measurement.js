/**
 * 关键尺寸测量功能
 */

class DimensionMeasurement {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            maxRows: 10,
            defaultRows: 3,
            materialNumber: '',
            inspectionId: null,
            inspectionType: 'sampling',
            ...options
        };
        
        this.measurements = [];
        this.templates = [];
        this.latestMeasurements = [];
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.loadTemplates();
        this.bindEvents();
    }
    
    createContainer() {
        this.container.innerHTML = `
            <div class="dimension-measurement-container">
                <div class="dimension-header">
                    <h3><i class="fas fa-ruler"></i> 关键尺寸测量</h3>
                    <div class="dimension-controls">
                        <button type="button" class="btn btn-sm btn-success" id="add-dimension-row">
                            <i class="fas fa-plus"></i> 添加尺寸
                        </button>
                        <button type="button" class="btn btn-sm btn-info" id="load-template">
                            <i class="fas fa-download"></i> 加载模板
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" id="load-with-data">
                            <i class="fas fa-history"></i> 加载含实测值
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" id="clear-measured-values">
                            <i class="fas fa-eraser"></i> 清空实测值
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" id="save-template">
                            <i class="fas fa-save"></i> 保存模板
                        </button>
                    </div>
                </div>
                
                <div class="dimension-table-container">
                    <table class="table table-bordered dimension-table">
                        <thead>
                            <tr>
                                <th style="width: 4%;">序号</th>
                                <th style="width: 10%;">位置</th>
                                <th style="width: 6%;">尺寸<br><span class="unit">(mm)</span></th>
                                <th style="width: 5%;">上公差<br><span class="unit">(mm)</span></th>
                                <th style="width: 5%;">下公差<br><span class="unit">(mm)</span></th>
                                <th style="width: 10%;">尺寸范围<br><span class="unit">(mm)</span></th>
                                <th style="width: 6%;">实测1<br><span class="unit">(mm)</span></th>
                                <th style="width: 6%;">实测2<br><span class="unit">(mm)</span></th>
                                <th style="width: 6%;">实测3<br><span class="unit">(mm)</span></th>
                                <th style="width: 6%;">实测4<br><span class="unit">(mm)</span></th>
                                <th style="width: 6%;">实测5<br><span class="unit">(mm)</span></th>
                                <th style="width: 5%;">结果</th>
                                <th style="width: 25%;">备注</th>
                                <th style="width: 4%;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="dimension-table-body">
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        // 初始化默认行数
        for (let i = 1; i <= this.options.defaultRows; i++) {
            this.addRow();
        }
    }
    
    addRow(data = {}) {
        const tbody = document.getElementById('dimension-table-body');
        const rowCount = tbody.children.length + 1;

        if (rowCount > this.options.maxRows) {
            alert(`最多只能添加${this.options.maxRows}个尺寸测量项`);
            return;
        }


        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="text-center">${rowCount}</td>
            <td>
                <input type="text" class="form-control form-control-sm position-input"
                       value="${data.position_name || ''}">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm dimension-input"
                       value="${data.nominal_dimension || ''}" step="0.0001">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm upper-tolerance-input"
                       value="${data.upper_tolerance || ''}" step="0.0001">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm lower-tolerance-input"
                       value="${data.lower_tolerance || ''}" step="0.0001">
            </td>
            <td>
                <span class="dimension-range">${data.dimension_range || '-'}</span>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm measured-input"
                       data-index="1" step="0.0001"
                       title="${this.getHistoryHint(data, 'measured_value_1')}"
                       value="${data.measured_value_1 || ''}">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm measured-input"
                       data-index="2" step="0.0001"
                       title="${this.getHistoryHint(data, 'measured_value_2')}"
                       value="${data.measured_value_2 || ''}">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm measured-input"
                       data-index="3" step="0.0001"
                       title="${this.getHistoryHint(data, 'measured_value_3')}"
                       value="${data.measured_value_3 || ''}">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm measured-input"
                       data-index="4" step="0.0001"
                       title="${this.getHistoryHint(data, 'measured_value_4')}"
                       value="${data.measured_value_4 || ''}">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm measured-input"
                       data-index="5" step="0.0001"
                       title="${this.getHistoryHint(data, 'measured_value_5')}"
                       value="${data.measured_value_5 || ''}">
            </td>
            <td>
                <select class="form-control form-control-sm result-select">
                    <option value="待定" ${data.result === '待定' ? 'selected' : ''}>待定</option>
                    <option value="合格" ${data.result === '合格' ? 'selected' : ''}>合格</option>
                    <option value="不合格" ${data.result === '不合格' ? 'selected' : ''}>不合格</option>
                    <option value="AOD" ${data.result === 'AOD' ? 'selected' : ''}>AOD</option>
                </select>
            </td>
            <td>
                <input type="text" class="form-control form-control-sm remarks-input"
                       value="${data.remarks || ''}">
            </td>
            <td class="text-center">
                <button type="button" class="btn btn-sm btn-danger remove-row" title="删除">
                    <i class="fas fa-minus"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
        this.bindRowEvents(row);
        this.updateRowNumbers();
        this.updateResultSelectBackground(row);
    }
    
    bindRowEvents(row) {
        // 绑定尺寸变化事件
        const dimensionInput = row.querySelector('.dimension-input');
        const upperToleranceInput = row.querySelector('.upper-tolerance-input');
        const lowerToleranceInput = row.querySelector('.lower-tolerance-input');
        const measuredInputs = row.querySelectorAll('.measured-input');
        const resultSelect = row.querySelector('.result-select');
        
        [dimensionInput, upperToleranceInput].forEach(input => {
            input.addEventListener('input', () => this.updateDimensionRange(row));
        });

        // 下公差输入框特殊处理，自动添加负号
        if (lowerToleranceInput) {
            lowerToleranceInput.addEventListener('input', () => {
                this.handleLowerToleranceInput(row);
                this.updateDimensionRange(row);
            });
        }
        
        // 绑定实测值变化事件
        measuredInputs.forEach(input => {
            input.addEventListener('input', () => this.autoCalculateResult(row));
        });
        
        // 绑定删除按钮事件
        const removeBtn = row.querySelector('.remove-row');
        removeBtn.addEventListener('click', () => this.removeRow(row));

        // 绑定结果选择框变化事件，更新背景色
        if (resultSelect) {
            resultSelect.addEventListener('change', () => {
                this.updateResultSelectBackground(row);
            });
        }
    }

    // 处理下公差输入，自动添加负号
    handleLowerToleranceInput(row) {
        const lowerToleranceInput = row.querySelector('.lower-tolerance-input');
        let value = parseFloat(lowerToleranceInput.value);

        if (!isNaN(value) && value > 0) {
            lowerToleranceInput.value = -value;
        }
    }

    updateDimensionRange(row) {
        const dimension = parseFloat(row.querySelector('.dimension-input').value) || 0;
        const upperTol = parseFloat(row.querySelector('.upper-tolerance-input').value) || 0;
        const lowerTol = parseFloat(row.querySelector('.lower-tolerance-input').value) || 0;

        const minValue = dimension + lowerTol;
        const maxValue = dimension + upperTol;

        const rangeSpan = row.querySelector('.dimension-range');
        rangeSpan.textContent = `${minValue.toFixed(4)} ~ ${maxValue.toFixed(4)}`;

        // 自动计算结果
        this.autoCalculateResult(row);
    }
    
    autoCalculateResult(row) {
        const dimension = parseFloat(row.querySelector('.dimension-input').value) || 0;
        const upperTol = parseFloat(row.querySelector('.upper-tolerance-input').value) || 0;
        let lowerTol = parseFloat(row.querySelector('.lower-tolerance-input').value) || 0;

        // 确保下公差为负数
        if (lowerTol > 0) {
            lowerTol = -lowerTol;
        }
        const measuredInputs = row.querySelectorAll('.measured-input');
        const resultSelect = row.querySelector('.result-select');
        
        if (dimension === 0) return;
        
        const minValue = dimension + lowerTol;
        const maxValue = dimension + upperTol;
        
        let result = '合格';
        
        for (let input of measuredInputs) {
            const value = parseFloat(input.value);
            if (!isNaN(value)) {
                if (value < minValue || value > maxValue) {
                    result = '不合格';
                    break;
                }
            }
        }
        
        resultSelect.value = result;
        this.updateResultSelectBackground(row);
    }
    
    removeRow(row) {
        const tbody = document.getElementById('dimension-table-body');
        if (tbody.children.length <= 1) {
            alert('至少需要保留一行');
            return;
        }
        
        row.remove();
        this.updateRowNumbers();
    }
    
    updateRowNumbers() {
        const tbody = document.getElementById('dimension-table-body');
        Array.from(tbody.children).forEach((row, index) => {
            row.firstElementChild.textContent = index + 1;
        });
    }
    
    getHistoryHint(data, field) {
        // 优先使用historyData中的数据
        const historyData = data.historyData || data;

        if (historyData && historyData[field]) {
            const value = historyData[field];
            const inspectionDate = historyData.inspection_date;

            if (inspectionDate) {
                return `上次检验(${inspectionDate}): ${value}`;
            } else {
                return `历史值: ${value}`;
            }
        }
        return '';
    }
    
    bindEvents() {
        // 添加行按钮
        document.getElementById('add-dimension-row').addEventListener('click', () => {
            this.addRow();
        });

        // 加载模板按钮（不自动填充实测值）
        document.getElementById('load-template').addEventListener('click', () => {
            this.loadTemplates(false);
        });

        // 加载含实测值按钮
        document.getElementById('load-with-data').addEventListener('click', () => {
            this.loadTemplates(true);
        });

        // 清空实测值按钮
        document.getElementById('clear-measured-values').addEventListener('click', () => {
            this.clearMeasuredValues();
        });

        // 保存模板按钮
        document.getElementById('save-template').addEventListener('click', () => {
            this.saveTemplate();
        });
    }

    clearMeasuredValues() {
        /**
         * 清空所有实测值，保留标准尺寸信息
         */
        const tbody = document.getElementById('dimension-table-body');
        Array.from(tbody.children).forEach(row => {
            // 清空实测值输入框
            row.querySelectorAll('.measured-input').forEach(input => {
                input.value = '';
            });

            // 重置结果为待定
            const resultSelect = row.querySelector('.result-select');
            if (resultSelect) {
                resultSelect.value = '待定';
            }

            // 清空备注
            const remarksInput = row.querySelector('.remarks-input');
            if (remarksInput) {
                remarksInput.value = '';
            }
        });

        alert('已清空所有实测值');
    }
    
    async loadTemplates(autoFillMeasuredValues = true) {
        if (!this.options.materialNumber) {
            alert('请先选择物料');
            return;
        }

        try {
            // 加载标准尺寸模板
            const templateResponse = await fetch(`/dimension/api/material_templates/${this.options.materialNumber}`);
            const templateData = await templateResponse.json();

            // 加载最近测量数据
            const measurementResponse = await fetch(`/dimension/api/latest_measurements/${this.options.materialNumber}`);
            const measurementData = await measurementResponse.json();

            if (templateData.success) {
                this.templates = templateData.templates;
                this.latestMeasurements = measurementData.success ? measurementData.measurements : [];



                // 清空现有行
                document.getElementById('dimension-table-body').innerHTML = '';

                // 加载模板数据
                if (this.templates.length > 0) {
                    this.templates.forEach((template, index) => {
                        // 查找对应的历史测量数据（确保类型匹配）
                        const historyData = this.latestMeasurements.find(m =>
                            parseInt(m.sequence_no) === parseInt(template.sequence_no));

                        let rowData = { ...template };

                        // 根据参数决定是否自动填充实测值
                        if (autoFillMeasuredValues && historyData) {
                            // 自动填充实测值（确保数值不为null或undefined）
                            rowData = {
                                ...template,
                                measured_value_1: historyData.measured_value_1 || '',
                                measured_value_2: historyData.measured_value_2 || '',
                                measured_value_3: historyData.measured_value_3 || '',
                                measured_value_4: historyData.measured_value_4 || '',
                                measured_value_5: historyData.measured_value_5 || '',
                                result: historyData.result || '待定',
                                remarks: historyData.remarks || ''
                            };
                        } else if (historyData) {
                            // 只保留历史数据作为提示，不自动填充
                            rowData.historyData = historyData;
                        }

                        this.addRow(rowData);
                    });
                } else {
                    // 如果没有模板，但有历史测量数据，尝试从历史数据创建行
                    if (this.latestMeasurements.length > 0 && autoFillMeasuredValues) {
                        this.latestMeasurements.forEach(measurement => {
                            this.addRow(measurement);
                        });
                    } else {
                        // 创建默认行
                        for (let i = 1; i <= this.options.defaultRows; i++) {
                            this.addRow();
                        }
                    }
                }
            }
        } catch (error) {
            console.error('加载模板失败:', error);
            alert('加载模板失败: ' + error.message);
        }
    }
    
    async saveTemplate() {
        if (!this.options.materialNumber) {
            alert('请先选择物料');
            return;
        }
        
        const templates = this.getMeasurementData().map((data, index) => ({
            sequence_no: index + 1,
            position_name: data.position_name,
            nominal_dimension: data.nominal_dimension,
            upper_tolerance: data.upper_tolerance,
            lower_tolerance: data.lower_tolerance
        })).filter(t => t.position_name && t.nominal_dimension);
        
        if (templates.length === 0) {
            alert('没有有效的尺寸数据可保存');
            return;
        }
        
        try {
            const response = await fetch('/dimension/api/update_template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    material_number: this.options.materialNumber,
                    templates: templates
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('标准尺寸模板保存成功');
            } else {
                alert('保存失败: ' + data.error);
            }
        } catch (error) {
            console.error('保存模板失败:', error);
            alert('保存模板失败: ' + error.message);
        }
    }
    
    getMeasurementData() {
        const tbody = document.getElementById('dimension-table-body');
        const data = [];
        
        Array.from(tbody.children).forEach((row, index) => {
            const rowData = {
                sequence_no: index + 1,
                position_name: row.querySelector('.position-input').value,
                nominal_dimension: parseFloat(row.querySelector('.dimension-input').value) || null,
                upper_tolerance: parseFloat(row.querySelector('.upper-tolerance-input').value) || null,
                lower_tolerance: parseFloat(row.querySelector('.lower-tolerance-input').value) || null,
                measured_value_1: parseFloat(row.querySelector('.measured-input[data-index="1"]').value) || null,
                measured_value_2: parseFloat(row.querySelector('.measured-input[data-index="2"]').value) || null,
                measured_value_3: parseFloat(row.querySelector('.measured-input[data-index="3"]').value) || null,
                measured_value_4: parseFloat(row.querySelector('.measured-input[data-index="4"]').value) || null,
                measured_value_5: parseFloat(row.querySelector('.measured-input[data-index="5"]').value) || null,
                result: row.querySelector('.result-select').value,
                remarks: row.querySelector('.remarks-input').value
            };
            
            // 只保存有效数据
            if (rowData.position_name || rowData.nominal_dimension) {
                data.push(rowData);
            }
        });
        
        return data;
    }
    
    setMaterialNumber(materialNumber) {
        this.options.materialNumber = materialNumber;
        // 默认加载含实测数据的模板，方便用户参考
        this.loadTemplates(true);
    }
    
    setInspectionInfo(inspectionId, inspectionType) {
        this.options.inspectionId = inspectionId;
        this.options.inspectionType = inspectionType;
    }
    
    async saveMeasurements(autoUpdateTemplate = false) {
        if (!this.options.inspectionId || !this.options.materialNumber) {
            return { success: false, error: '缺少检验ID或物料编号' };
        }

        const measurements = this.getMeasurementData();

        if (measurements.length === 0) {
            return { success: true, message: '没有尺寸测量数据需要保存' };
        }

        try {
            const response = await fetch('/dimension/api/save_measurements', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inspection_id: this.options.inspectionId,
                    inspection_type: this.options.inspectionType,
                    material_number: this.options.materialNumber,
                    measurements: measurements
                })
            });

            const data = await response.json();

            // 如果保存成功且需要自动更新模板
            if (data.success && autoUpdateTemplate) {
                const templateResult = await this.autoUpdateTemplate();
                if (templateResult.success) {
                    data.message += ' 标准尺寸模板已自动更新。';
                } else {
                    data.message += ' 但模板更新失败: ' + templateResult.error;
                }
            }

            return data;

        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async autoUpdateTemplate() {
        /**
         * 自动更新物料标准尺寸模板
         * 只更新标准尺寸、公差等基础信息，不包含实测值
         */
        if (!this.options.materialNumber) {
            return { success: false, error: '缺少物料编号' };
        }

        const measurements = this.getMeasurementData();
        const templates = measurements.map((data, index) => ({
            sequence_no: index + 1,
            position_name: data.position_name,
            nominal_dimension: data.nominal_dimension,
            upper_tolerance: data.upper_tolerance,
            lower_tolerance: data.lower_tolerance
        })).filter(t => t.position_name && t.nominal_dimension);

        if (templates.length === 0) {
            return { success: true, message: '没有有效的尺寸数据可更新到模板' };
        }

        try {
            const response = await fetch('/dimension/api/update_template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    material_number: this.options.materialNumber,
                    templates: templates
                })
            });

            const data = await response.json();
            return data;

        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 更新结果选择框背景色
    updateResultSelectBackground(row) {
        const resultSelect = row.querySelector('.result-select');
        if (!resultSelect) return;

        const value = resultSelect.value;

        // 清除所有背景色类
        resultSelect.removeAttribute('data-value');
        resultSelect.style.backgroundColor = '';
        resultSelect.style.color = '';

        // 根据选中值设置背景色
        switch(value) {
            case '合格':
                resultSelect.style.backgroundColor = '#d4edda';
                resultSelect.style.color = '#155724';
                break;
            case '不合格':
                resultSelect.style.backgroundColor = '#f8d7da';
                resultSelect.style.color = '#721c24';
                break;
            case '待定':
                resultSelect.style.backgroundColor = '#fff3cd';
                resultSelect.style.color = '#856404';
                break;
            case 'AOD':
                resultSelect.style.backgroundColor = '#fff3cd';
                resultSelect.style.color = '#856404';
                break;
            default:
                resultSelect.style.backgroundColor = 'transparent';
                resultSelect.style.color = '';
        }
    }
}

// 全局函数，供外部调用
window.DimensionMeasurement = DimensionMeasurement;
